#!/usr/bin/env python3
"""
测试题型配置修复的脚本
验证GUI设置与运行时配置的一致性
"""

import sys
import os
import json
import tempfile
import tkinter as tk
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from utils.question_calculator import QuestionCalculator
from gui.components.question_types_config_panel import QuestionTypesConfigPanel

def test_config_defaults():
    """测试配置默认值的一致性"""
    print("=== 测试配置默认值 ===")
    
    # 测试Config类的默认值
    config = Config()
    print(f"Config默认值:")
    print(f"  单选题: {config.SINGLE_CHOICE_COUNT}")
    print(f"  多选题: {config.MULTIPLE_CHOICE_COUNT}")
    print(f"  填空题: {config.FILL_BLANK_COUNT}")
    print(f"  简答题: {config.SHORT_ANSWER_COUNT}")
    print(f"  判断题: {config.TRUE_FALSE_COUNT}")
    print(f"  排序题: {config.SORTING_COUNT}")
    
    # 测试GUI组件的默认值
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建变量
    single_choice_var = tk.IntVar(value=2)
    multiple_choice_var = tk.IntVar(value=1)
    fill_blank_var = tk.IntVar(value=1)
    short_answer_var = tk.IntVar(value=1)
    true_false_var = tk.IntVar(value=1)
    sorting_var = tk.IntVar(value=0)
    
    frame = tk.Frame(root)
    panel = QuestionTypesConfigPanel(
        frame,
        single_choice_var=single_choice_var,
        multiple_choice_var=multiple_choice_var,
        fill_blank_var=fill_blank_var,
        short_answer_var=short_answer_var,
        true_false_var=true_false_var,
        sorting_var=sorting_var
    )
    
    print(f"GUI默认值:")
    print(f"  单选题: {panel.single_choice_var.get()}")
    print(f"  多选题: {panel.multiple_choice_var.get()}")
    print(f"  填空题: {panel.fill_blank_var.get()}")
    print(f"  简答题: {panel.short_answer_var.get()}")
    print(f"  判断题: {panel.true_false_var.get()}")
    print(f"  排序题: {panel.sorting_var.get()}")
    
    root.destroy()
    
    # 验证一致性
    assert config.SINGLE_CHOICE_COUNT == single_choice_var.get(), "单选题默认值不一致"
    assert config.MULTIPLE_CHOICE_COUNT == multiple_choice_var.get(), "多选题默认值不一致"
    assert config.FILL_BLANK_COUNT == fill_blank_var.get(), "填空题默认值不一致"
    assert config.SHORT_ANSWER_COUNT == short_answer_var.get(), "简答题默认值不一致"
    assert config.TRUE_FALSE_COUNT == true_false_var.get(), "判断题默认值不一致"
    assert config.SORTING_COUNT == sorting_var.get(), "排序题默认值不一致"
    
    print("配置默认值一致性测试通过")

def test_config_persistence():
    """测试配置持久化"""
    print("\n=== 测试配置持久化 ===")
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        
        # 创建测试配置
        test_config_data = {
            "api": {
                "base_url": "http://test.com",
                "key": "test-key",
                "model": "test-model",
                "max_tokens": 1000,
                "temperature": 0.5,
                "timeout": 30,
                "max_retries": 2,
                "retry_delay": 1,
                "api_rate_limit": 1.0
            },
            "processing": {
                "max_chunk_size": 3000,
                "disable_document_splitting": False,
                "enable_chunk_merging": True,
                "use_new_splitting_logic": True,
                "base_char_threshold": 1500,
                "single_choice_count": 3,
                "multiple_choice_count": 2,
                "fill_blank_count": 2,
                "short_answer_count": 1,
                "true_false_count": 2,
                "sorting_count": 1
            },
            "file_types": {
                "enable_docx": True,
                "enable_md": True,
                "enable_txt": True,
                "enable_pdf": True
            },
            "output": {
                "dir": "test_output",
                "error_dir": "test_errors",
                "csv_filename": "test_results.csv",
                "save_interval": 5,
                "csv_batch_save_count": 10,
                "auto_backup_interval": 50
            },
            "ui": {
                "file_path": "/test/path",
                "recursive": True
            }
        }
        
        json.dump(test_config_data, f, indent=2)
    
    try:
        # 从文件加载配置
        config = Config.from_json_file(temp_config_path)
        
        print(f"加载的配置:")
        print(f"  单选题: {config.SINGLE_CHOICE_COUNT}")
        print(f"  多选题: {config.MULTIPLE_CHOICE_COUNT}")
        print(f"  填空题: {config.FILL_BLANK_COUNT}")
        print(f"  简答题: {config.SHORT_ANSWER_COUNT}")
        print(f"  判断题: {config.TRUE_FALSE_COUNT}")
        print(f"  排序题: {config.SORTING_COUNT}")
        
        # 验证配置值
        assert config.SINGLE_CHOICE_COUNT == 3, f"单选题配置错误: {config.SINGLE_CHOICE_COUNT}"
        assert config.MULTIPLE_CHOICE_COUNT == 2, f"多选题配置错误: {config.MULTIPLE_CHOICE_COUNT}"
        assert config.FILL_BLANK_COUNT == 2, f"填空题配置错误: {config.FILL_BLANK_COUNT}"
        assert config.SHORT_ANSWER_COUNT == 1, f"简答题配置错误: {config.SHORT_ANSWER_COUNT}"
        assert config.TRUE_FALSE_COUNT == 2, f"判断题配置错误: {config.TRUE_FALSE_COUNT}"
        assert config.SORTING_COUNT == 1, f"排序题配置错误: {config.SORTING_COUNT}"
        
        print("配置持久化测试通过")
        
    finally:
        # 清理临时文件
        os.unlink(temp_config_path)

def test_question_calculator_update():
    """测试题目计算器配置更新"""
    print("\n=== 测试题目计算器配置更新 ===")
    
    # 创建初始配置
    config1 = Config()
    config1.SINGLE_CHOICE_COUNT = 2
    config1.MULTIPLE_CHOICE_COUNT = 1
    config1.FILL_BLANK_COUNT = 1
    config1.SHORT_ANSWER_COUNT = 1
    config1.TRUE_FALSE_COUNT = 1
    config1.SORTING_COUNT = 0
    
    calculator = QuestionCalculator(config1)
    
    print(f"初始配置:")
    print(f"  单选题: {calculator.base_single_choice}")
    print(f"  多选题: {calculator.base_multiple_choice}")
    print(f"  填空题: {calculator.base_fill_blank}")
    print(f"  简答题: {calculator.base_short_answer}")
    print(f"  判断题: {calculator.base_true_false}")
    print(f"  排序题: {calculator.base_sorting}")
    
    # 更新配置
    config2 = Config()
    config2.SINGLE_CHOICE_COUNT = 4
    config2.MULTIPLE_CHOICE_COUNT = 2
    config2.FILL_BLANK_COUNT = 2
    config2.SHORT_ANSWER_COUNT = 1
    config2.TRUE_FALSE_COUNT = 2
    config2.SORTING_COUNT = 1
    
    calculator.update_config(config2)
    
    print(f"更新后配置:")
    print(f"  单选题: {calculator.base_single_choice}")
    print(f"  多选题: {calculator.base_multiple_choice}")
    print(f"  填空题: {calculator.base_fill_blank}")
    print(f"  简答题: {calculator.base_short_answer}")
    print(f"  判断题: {calculator.base_true_false}")
    print(f"  排序题: {calculator.base_sorting}")
    
    # 验证更新
    assert calculator.base_single_choice == 4, "单选题更新失败"
    assert calculator.base_multiple_choice == 2, "多选题更新失败"
    assert calculator.base_fill_blank == 2, "填空题更新失败"
    assert calculator.base_short_answer == 1, "简答题更新失败"
    assert calculator.base_true_false == 2, "判断题更新失败"
    assert calculator.base_sorting == 1, "排序题更新失败"
    
    print("题目计算器配置更新测试通过")

def test_question_calculation():
    """测试题目数量计算"""
    print("\n=== 测试题目数量计算 ===")
    
    config = Config()
    config.SINGLE_CHOICE_COUNT = 2
    config.MULTIPLE_CHOICE_COUNT = 1
    config.FILL_BLANK_COUNT = 1
    config.SHORT_ANSWER_COUNT = 1
    config.TRUE_FALSE_COUNT = 1
    config.SORTING_COUNT = 0
    config.BASE_CHAR_THRESHOLD = 2000
    
    calculator = QuestionCalculator(config)
    
    # 测试不同字符数的计算结果
    test_cases = [
        (1000, "小于阈值"),
        (2000, "等于阈值"),
        (4000, "两倍阈值"),
        (6000, "三倍阈值")
    ]
    
    for char_count, description in test_cases:
        result = calculator.calculate_question_counts(char_count)
        total = sum(result.values())
        print(f"{description}({char_count}字符): 总题目{total}道 - {result}")
    
    print("题目数量计算测试通过")

if __name__ == "__main__":
    print("开始测试题型配置修复...")
    
    try:
        test_config_defaults()
        test_config_persistence()
        test_question_calculator_update()
        test_question_calculation()
        
        print("\n所有测试通过！题型配置修复成功！")

    except Exception as e:
        print(f"\n测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
