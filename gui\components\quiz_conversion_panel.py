import sys
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QFileDialog, QMessageBox, QProgressBar
from PyQt5.QtCore import Qt, QThread, pyqtSignal
import logging

# Adjust the import path for QuizFormatConverter
from quiz_converter.quiz_formatter import QuizFormatConverter

class QuizConversionWorker(QThread):
    finished = pyqtSignal(dict)

    def __init__(self, source_file, target_file):
        super().__init__()
        self.source_file = source_file
        self.target_file = target_file

    def run(self):
        converter = QuizFormatConverter(self.source_file, self.target_file)
        result = converter.convert()
        self.finished.emit(result)


class QuizConversionPanel(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        main_layout = QVBoxLayout()

        # Source file selection
        source_layout = QHBoxLayout()
        self.source_label = QLabel("源文件 (output/quiz_results.csv):")
        self.source_input = QLineEdit()
        self.source_button = QPushButton("选择文件")
        self.source_button.clicked.connect(self.select_source_file)
        source_layout.addWidget(self.source_label)
        source_layout.addWidget(self.source_input)
        source_layout.addWidget(self.source_button)
        main_layout.addLayout(source_layout)

        # Target file selection
        target_layout = QHBoxLayout()
        self.target_label = QLabel("目标文件 (客观题导入模板_utf8.csv): ")
        self.target_input = QLineEdit()
        # Set default target file name
        self.target_input.setText("客观题导入模板_utf8.csv")
        self.target_button = QPushButton("选择路径")
        self.target_button.clicked.connect(self.select_target_path)
        target_layout.addWidget(self.target_label)
        target_layout.addWidget(self.target_input)
        target_layout.addWidget(self.target_button)
        main_layout.addLayout(target_layout)

        # Conversion button and progress
        self.convert_button = QPushButton("开始转换")
        self.convert_button.clicked.connect(self.start_conversion)
        main_layout.addWidget(self.convert_button)

        self.progress_bar = QProgressBar(self)
        self.progress_bar.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.progress_bar)

        self.result_label = QLabel("状态: 等待操作")
        main_layout.addWidget(self.result_label)

        self.setLayout(main_layout)

    def select_source_file(self):
        file_dialog = QFileDialog()
        file_name, _ = file_dialog.getOpenFileName(self, "选择源 CSV 文件", "", "CSV Files (*.csv)")
        if file_name:
            self.source_input.setText(file_name)

    def select_target_path(self):
        # Get the default file name from the QLineEdit
        default_file_name = self.target_input.text()
        # Use QFileDialog.getSaveFileName for target file, allowing user to specify name and path
        file_name, _ = QFileDialog.getSaveFileName(self, "保存目标 CSV 文件", default_file_name, "CSV Files (*.csv)")
        if file_name:
            self.target_input.setText(file_name)

    def start_conversion(self):
        source_path = self.source_input.text()
        target_path = self.target_input.text()

        if not source_path or not target_path:
            QMessageBox.warning(self, "警告", "请选择源文件和目标文件。")
            return
        
        # Disable button during conversion
        self.convert_button.setEnabled(False)
        self.result_label.setText("状态: 转换中...")
        self.progress_bar.setValue(0)

        self.worker = QuizConversionWorker(source_path, target_path)
        self.worker.finished.connect(self.conversion_finished)
        self.worker.start()

    def conversion_finished(self, result):
        self.convert_button.setEnabled(True) # Re-enable button
        if result["success"]:
            self.result_label.setText(f"状态: 转换成功！共转换 {result['converted_count']} 条，跳过 {result['skipped_count']} 条。")
            self.progress_bar.setValue(100)
            QMessageBox.information(self, "完成", self.result_label.text())
        else:
            self.result_label.setText(f"状态: 转换失败！{result['message']}")
            self.progress_bar.setValue(0)
            QMessageBox.critical(self, "错误", self.result_label.text())
            logging.error(f"题库转换失败: {result['message']}")

# For testing the panel independently
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    app = QApplication(sys.argv)
    panel = QuizConversionPanel()
    panel.show()
    sys.exit(app.exec_()) 