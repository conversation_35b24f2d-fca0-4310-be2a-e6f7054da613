import csv
import codecs
import logging
import re
import os

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')


class QuizToTxtConverter:
    def __init__(self, config_path="新模板.ini"):
        self.config = self._load_config(config_path)
        self.ini_header_content = self._read_ini_header(config_path)

    def _load_config(self, config_path):
        """
        以文本方式读取新模板.ini，提取题型前缀和标签，存入字典。
        """
        config = {}
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            # 提取题型前缀
            config['single_choice_prefix'] = re.search(r'【单选题】', content).group(0) if re.search(r'【单选题】', content) else '【单选题】'
            config['multiple_choice_prefix'] = re.search(r'【多选题】', content).group(0) if re.search(r'【多选题】', content) else '【多选题】'
            config['true_false_prefix'] = re.search(r'【判断题】', content).group(0) if re.search(r'【判断题】', content) else '【判断题】'
            config['fill_in_blank_prefix'] = re.search(r'【填空题】', content).group(0) if re.search(r'【填空题】', content) else '【填空题】'
            config['short_answer_prefix'] = re.search(r'【简答题】', content).group(0) if re.search(r'【简答题】', content) else '【简答题】'
            # 提取标签
            def find_label(label, default):
                m = re.search(rf'{label}：', content)
                return m.group(0) if m else f'{label}：'
            config['correct_answer_label'] = find_label('正确答案', '正确答案')
            config['difficulty_label'] = find_label('题目难度', '题目难度')
            config['explanation_label'] = find_label('答案解析', '答案解析')
            config['knowledge_points_label'] = find_label('知识点', '知识点')
            config['no_knowledge_points'] = '暂无知识点'
            config['fill_in_blank_upload_image_label'] = find_label('作答上传图片', '作答上传图片')
            config['fill_in_blank_upload_image_value'] = '否'
            config['short_answer_keyword_label'] = '关键字'
            config['short_answer_upload_image_label'] = find_label('作答上传图片', '作答上传图片')
            config['short_answer_upload_image_value'] = '否'
        except Exception as e:
            logging.warning(f"加载配置文件 {config_path} 失败: {e}。将使用默认配置。")
            # 默认配置
            config = {
                'single_choice_prefix': '【单选题】',
                'multiple_choice_prefix': '【多选题】',
                'true_false_prefix': '【判断题】',
                'fill_in_blank_prefix': '【填空题】',
                'short_answer_prefix': '【简答题】',
                'correct_answer_label': '正确答案：',
                'difficulty_label': '题目难度：',
                'explanation_label': '答案解析：',
                'knowledge_points_label': '知识点：',
                'no_knowledge_points': '暂无知识点',
                'fill_in_blank_upload_image_label': '作答上传图片：',
                'fill_in_blank_upload_image_value': '否',
                'short_answer_keyword_label': '关键字',
                'short_answer_upload_image_label': '作答上传图片：',
                'short_answer_upload_image_value': '否',
            }
        return config

    def _read_ini_header(self, ini_file_path):
        """
        读取INI文件的内容作为头部，并移除其中的示例题目部分。
        """
        try:
            with open(ini_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            cleaned_lines = []
            found_example_start = False
            for line in lines:
                # 检查是否是示例开始的行，例如"1.【单选题】（示例导入前请删除）"
                if re.match(r'^\s*\d+\.【.*】.*（示例导入前请删除）', line):
                    found_example_start = True
                    break # 找到示例开始，跳出循环，只保留之前的内容

                cleaned_lines.append(line)
            
            # 将清理后的行组合成字符串，并去除首尾空白符
            content = "".join(cleaned_lines).strip()
            
            logging.info(f"成功读取并处理INI文件头部内容: {ini_file_path}")
            return content + "\n\n\n" # 添加三个换行符确保与后续题目内容分隔，并与题目间的空行一致
        except FileNotFoundError:
            logging.error(f"INI文件未找到: {ini_file_path}")
            return ""
        except Exception as e:
            logging.error(f"读取INI文件头部时发生错误: {e}")
            return ""

    def _read_csv(self, csv_file_path):
        logging.info(f"正在读取CSV文件: {csv_file_path}")
        try:
            # 检查文件是否有UTF-8 BOM
            with open(csv_file_path, 'rb') as f:
                header = f.read(3)
                if header == codecs.BOM_UTF8:
                    encoding = 'utf-8-sig'
                else:
                    encoding = 'utf-8'

            data = []
            with open(csv_file_path, 'r', encoding=encoding) as f:
                reader = csv.DictReader(f)
                logging.debug(f"CSV Headers (keys): {reader.fieldnames!r}")
                for row in reader:
                    # 对每一行的所有值进行strip处理，去除两端空白符（包括换行符）
                    cleaned_row = {k: v.strip() if isinstance(v, str) else v for k, v in row.items()}
                    data.append(cleaned_row)
            logging.info(f"成功读取 {len(data)} 行数据。")
            return data
        except FileNotFoundError:
            logging.error(f"文件未找到: {csv_file_path}")
            raise
        except Exception as e:
            logging.error(f"读取CSV文件时发生错误: {e}")
            raise

    def _parse_question_data(self, row):
        # 定义CSV列名变量，避免直接在row.get中使用复杂字符串，防止解析错误
        col_question_type = '试题类型(必填，题型请用下拉菜单实现）'
        col_question_text = '试题题干(必填)'
        col_options = "选项（用'|'隔开）"
        col_answer = "答案（填空题用'|'隔开）(必填)"
        col_score = '分数'
        col_difficulty = '难易度 (必填，难易度请选择下拉菜单实现)'
        col_explanation = '试题解析'
        col_knowledge_points = '知识点'

        question_type = str(row.get(col_question_type, '')).strip()
        logging.debug(f"问题类型 (repr): {question_type!r}")
        # 题型名称标准化映射
        type_mapping = {
            '单选': '单选题',
            '多选': '多选题',
            '问答': '简答题', # 将 '问答' 映射到 '简答题'
            '判断': '判断题',
            '填空': '填空题',
            '简答': '简答题',
            '排序': '排序题', # 添加排序题映射，但尚未实现格式化
        }
        standardized_type = type_mapping.get(question_type, question_type) # 如果没有映射，则使用原始值

        question_text = str(row.get(col_question_text, '')).strip().replace('\n', ' ').replace('\r', ' ')
        logging.debug(f"问题文本 (repr): {question_text!r}")
        raw_answer = str(row.get(col_answer, '')).strip().replace('\n', ' ').replace('\r', ' ')
        logging.debug(f"原始答案 (repr): {raw_answer!r}")
        explanation = str(row.get(col_explanation, '')).strip().replace('\n', ' ').replace('\r', ' ')
        difficulty = str(row.get(col_difficulty, '')).strip().replace('\n', ' ').replace('\r', ' ')
        knowledge_points_raw = str(row.get(col_knowledge_points, '')).strip().replace('\n', ' ').replace('\r', ' ')
        knowledge_points = [kp.strip() for kp in knowledge_points_raw.split('|') if kp.strip()] if knowledge_points_raw else []

        parsed_data = {
            'type': standardized_type, # 使用标准化后的题型
            'question': question_text,
            'explanation': explanation,
            'difficulty': difficulty,
            'knowledge_points': knowledge_points
        }

        if standardized_type in ['单选题', '多选题']:
            parsed_data['answer'] = raw_answer # 单选多选答案直接使用
            options = {}
            # 从 '选项（用'|'隔开）' 列获取选项
            options_raw = str(row.get(col_options, '')).strip().replace('\n', ' ').replace('\r', ' ')
            logging.debug(f"选项原始字符串 (repr): {options_raw!r}")
            if options_raw:
                # 选项分隔符是 '|'，但选项内容本身可能包含'|'，需要仔细处理
                # 这里假设选项格式是 'A. 选项内容|B. 选项内容' 或 'A.选项内容|B.选项内容'
                option_pairs = options_raw.split('|')
                for pair in option_pairs:
                    # 尝试使用中文句号或英文句号作为选项编号和内容的分隔符
                    if '．' in pair:
                        parts = pair.split('．', 1)
                    elif '.' in pair: # 允许没有空格的 . 分隔
                        parts = pair.split('.', 1)
                    else:
                        continue # 如果没有明确的选项分隔符，跳过

                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        # 确保 key 是单个大写字母，并且在 A-L 范围内
                        if len(key) == 1 and 'A' <= key <= 'L':
                            options[key] = value
            parsed_data['options'] = options
            logging.debug(f"Parsed options (repr): {options!r}")
        elif standardized_type == '填空题':
            logging.debug(f"处理填空题，原始答案 (repr): {raw_answer!r}, 类型: {type(raw_answer)}")
            blank_answer_strings = raw_answer.split('|') if '|' in raw_answer else [raw_answer]
            logging.debug(f"填空题分割后的答案字符串 (repr): {blank_answer_strings!r}")
            parsed_answers_for_blanks = []
            for blank_ans_str in blank_answer_strings:
                logging.debug(f"处理单个填空答案字符串 (repr): {blank_ans_str!r}")
                parsed_options = self._parse_fill_in_blank_answer_options_for_one_blank(blank_ans_str)
                parsed_answers_for_blanks.append(parsed_options)
            parsed_data['answer'] = parsed_answers_for_blanks # Now it's list of lists
        elif standardized_type == '简答题':
            # 对于简答题，答案是关键词
            keywords_raw = raw_answer # 简答题的关键词从统一的答案列获取
            logging.debug(f"简答题原始关键词 (repr): {keywords_raw!r}")
            if keywords_raw:
                # 根据用户最新指示，一个关键词对应一个词，不再处理 '{/}' 转义。
                # 只按 '|' 分割不同的关键词。
                parsed_data['keywords'] = [kw.strip() for kw in keywords_raw.split('|') if kw.strip()]
                logging.debug(f"最终分割结果 (repr): {parsed_data['keywords']!r}")
            else:
                parsed_data['keywords'] = []
            # 删除 answer 键，因为简答题用 keywords
            if 'answer' in parsed_data:
                del parsed_data['answer']
        elif standardized_type == '判断题':
            parsed_data['answer'] = raw_answer # 判断题答案直接使用
        else:
            # 其他未处理的题型，例如 '排序'，暂时不设置 answer 或 keywords，让 _format_question 处理
            pass

        logging.debug(f"解析题目数据: {parsed_data['question'][:50]}... (类型: {parsed_data['type']})")
        return parsed_data

    def _parse_fill_in_blank_answer_options_for_one_blank(self, option_string):
        options = []
        current_option_chars = []
        i = 0
        while i < len(option_string):
            if option_string[i] == '/' and (i == 0 or option_string[i-1] != '{'):
                options.append("".join(current_option_chars))
                current_option_chars = []
                i += 1
            elif option_string[i:i+3] == '{/}':
                current_option_chars.append('/')
                i += 3
            else:
                current_option_chars.append(option_string[i])
                i += 1
        if current_option_chars:
            options.append("".join(current_option_chars))
        return options

    def _adjust_question_type(self, question_data):
        original_type = question_data['type']
        adjusted_info = None # 用于存储调整前后的题目信息

        if original_type == '单选题':
            # 检查单选题是否有多个答案
            answer_str = question_data.get('answer', '')
            # 假设多答案通过CSV中答案列的长度大于1来判断
            # 或者更严谨地，可以检查答案字符串中是否包含非预期字符（如空格或分隔符）
            if len(answer_str) > 1 and all(c.isalpha() and 'A' <= c <= 'L' for c in answer_str):
                question_data['type'] = '多选题'
                adjusted_info = {
                    'question': question_data.get('question'),
                    'original_type': original_type,
                    'adjusted_type': '多选题',
                    'answer': answer_str,
                    'options': question_data.get('options')
                }
                logging.info(f"将单选题调整为多选题：题目 '{question_data.get('question', '')[:50]}...' 原始答案 '{answer_str}'")
        elif original_type == '多选题':
            # 检查多选题是否只有一个答案
            answer_str = question_data.get('answer', '')
            if len(answer_str) == 1 and 'A' <= answer_str[0] <= 'L': # 确保是单个字母答案
                question_data['type'] = '单选题'
                adjusted_info = {
                    'question': question_data.get('question'),
                    'original_type': original_type,
                    'adjusted_type': '单选题',
                    'answer': answer_str,
                    'options': question_data.get('options')
                }
                logging.info(f"将多选题调整为单选题：题目 '{question_data.get('question', '')[:50]}...' 原始答案 '{answer_str}'")
        
        return adjusted_info # 返回调整前后的信息，如果未调整则返回None

    def _escape_slash_for_template(self, s):
        return s.replace('/', '{/}')

    def _format_question(self, question_data, question_number):
        q_type = question_data.get('type')
        question_text = question_data.get('question', '')

        # 检查是否包含"文件编号"关键词，如果是，则忽略该题目
        if "文件编号" in question_text:
            logging.info(f"忽略询问文件编号的题目: {question_text[:50]}...")
            return ""

        if q_type == '单选题':
            return self._format_single_choice(question_data, question_number)
        elif q_type == '多选题':
            return self._format_multiple_choice(question_data, question_number)
        elif q_type == '判断题':
            return self._format_true_false(question_data, question_number)
        elif q_type == '填空题':
            return self._format_fill_in_blank(question_data, question_number)
        elif q_type == '简答题':
            return self._format_short_answer(question_data, question_number)
        elif q_type == '排序题':
            logging.info(f"忽略排序题 (根据用户指示): {question_data.get('question', '')[:50]}...")
            return ""
        else:
            logging.warning(f"未知题型: {q_type}. 题目: {question_data.get('question', '')[:50]}...")
            return ""

    def _format_single_choice(self, question_data, question_number):
        logging.debug(f"格式化单选题: {question_data.get('question', '')[:50]}...")
        c = self.config
        question_text = question_data.get('question', '')
        options = question_data.get('options', {})
        answer = question_data.get('answer', '')
        explanation = question_data.get('explanation', '')
        difficulty = question_data.get('difficulty', '')
        knowledge_points = question_data.get('knowledge_points', [])

        formatted_options = "\n".join([f"{k}、{v}" for k, v in sorted(options.items())])
        formatted_knowledge_points = "、".join(knowledge_points) if knowledge_points else c['no_knowledge_points']

        return (
            f"{question_number}.{c['single_choice_prefix']}{question_text}\n"
            f"{formatted_options}\n"
            f"{c['correct_answer_label']} {answer}\n"
            f"{c['difficulty_label']}{difficulty if difficulty else '简单'}\n"
            f"{c['explanation_label']}{explanation if explanation else '暂无解析'}\n"
            f"{c['knowledge_points_label']}{formatted_knowledge_points}\n"
        )

    def _format_multiple_choice(self, question_data, question_number):
        logging.debug(f"格式化多选题: {question_data.get('question', '')[:50]}...")
        c = self.config
        question_text = question_data.get('question', '')
        options = question_data.get('options', {})
        answer = question_data.get('answer', '')
        explanation = question_data.get('explanation', '')
        difficulty = question_data.get('difficulty', '')
        knowledge_points = question_data.get('knowledge_points', [])

        formatted_options = "\n".join([f"{k}、{v}" for k, v in sorted(options.items())])
        formatted_knowledge_points = "、".join(knowledge_points) if knowledge_points else c['no_knowledge_points']

        return (
            f"{question_number}.{c['multiple_choice_prefix']}{question_text}\n"
            f"{formatted_options}\n"
            f"{c['correct_answer_label']}{answer}\n"
            f"{c['difficulty_label']}{difficulty if difficulty else '简单'}\n"
            f"{c['explanation_label']}{explanation if explanation else '暂无解析'}\n"
            f"{c['knowledge_points_label']}{formatted_knowledge_points}\n"
        )

    def _format_true_false(self, question_data, question_number):
        logging.debug(f"格式化判断题: {question_data.get('question', '')[:50]}...")
        c = self.config
        question_text = question_data.get('question', '')
        answer = question_data.get('answer', '')
        explanation = question_data.get('explanation', '')
        difficulty = question_data.get('difficulty', '')
        knowledge_points = question_data.get('knowledge_points', [])

        formatted_knowledge_points = "、".join(knowledge_points) if knowledge_points else c['no_knowledge_points']

        return (
            f"{question_number}.{c['true_false_prefix']}{question_text}\n"
            f"{c['correct_answer_label']}{answer}\n"
            f"{c['difficulty_label']}{difficulty if difficulty else '简单'}\n"
            f"{c['explanation_label']}{explanation if explanation else '暂无解析'}\n"
            f"{c['knowledge_points_label']}{formatted_knowledge_points}\n"
        )

    def _format_fill_in_blank(self, question_data, question_number):
        logging.debug(f"格式化填空题: {question_data.get('question', '')[:50]}...")
        c = self.config
        question_text = question_data.get('question', '')
        answers_for_blanks = question_data.get('answer', [])
        explanation = question_data.get('explanation', '')
        difficulty = question_data.get('difficulty', '')
        knowledge_points = question_data.get('knowledge_points', [])

        formatted_knowledge_points = "、".join(knowledge_points) if knowledge_points else c['no_knowledge_points']

        blank_index = 0
        formatted_question_parts = []
        last_end = 0
        # 匹配英文括号作为填空位，并允许括号内有空白符
        for match in re.finditer(r'（\s*）', question_text):
            start, end = match.span()
            formatted_question_parts.append(question_text[last_end:start])

            if blank_index < len(answers_for_blanks):
                current_blank_options = answers_for_blanks[blank_index]
                # 填空题的答案不需要像简答题那样处理 {/} 转义，直接用 / 分隔即可
                formatted_options = "/".join(current_blank_options)
                formatted_question_parts.append(f"【{formatted_options}】")
            else:
                logging.warning(f"填空题答案数量与题目中的空不匹配。题目: {question_text}")
                formatted_question_parts.append("【】")

            last_end = end
            blank_index += 1
        formatted_question_parts.append(question_text[last_end:])

        final_question_text = "".join(formatted_question_parts)

        return (
            f"{question_number}.{c['fill_in_blank_prefix']}{final_question_text}\n"
            f"{c['difficulty_label']}{difficulty if difficulty else '简单'}\n"
            f"{c['fill_in_blank_upload_image_label']}{c['fill_in_blank_upload_image_value']}\n"
            f"{c['explanation_label']}{explanation if explanation else '暂无解析'}\n"
            f"{c['knowledge_points_label']}{formatted_knowledge_points}\n"
        )

    def _format_short_answer(self, question_data, question_number):
        logging.debug(f"格式化简答题: {question_data.get('question', '')[:50]}...")
        c = self.config
        question_text = question_data.get('question', '')
        keywords = question_data.get('keywords', [])
        explanation = question_data.get('explanation', '')
        difficulty = question_data.get('difficulty', '')
        knowledge_points = question_data.get('knowledge_points', [])

        formatted_keywords = []
        for i, kw_group in enumerate(keywords):
            formatted_keywords.append(f"{c['short_answer_keyword_label']}{i + 1}：{kw_group}")

        formatted_knowledge_points = "、".join(knowledge_points) if knowledge_points else c['no_knowledge_points']
        keywords_string = "\n".join(formatted_keywords)

        return (
            f"{question_number}.{c['short_answer_prefix']}{question_text}\n"
            f"{keywords_string}\n"
            f"{c['difficulty_label']}{difficulty if difficulty else '困难'}\n"
            f"{c['short_answer_upload_image_label']}{c['short_answer_upload_image_value']}\n"
            f"{c['explanation_label']}{explanation if explanation else '暂无解析'}\n"
            f"{c['knowledge_points_label']}{formatted_knowledge_points}\n"
        )

    def _write_txt(self, content, output_file_path):
        logging.info(f"正在写入TXT文件: {output_file_path}")
        try:
            output_dir = os.path.dirname(output_file_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)
                logging.info(f"已创建输出目录: {output_dir}")
            with open(output_file_path, 'w', encoding='utf-8') as f:
                # 先写入INI文件头部内容
                if self.ini_header_content:
                    f.write(self.ini_header_content)
                f.write(content)
            logging.info(f"TXT文件写入成功: {output_file_path}")
        except Exception as e:
            logging.error(f"写入TXT文件时发生错误: {e}")
            raise

    def convert(self, csv_file_path, output_dir):
        logging.info(f"开始转换CSV文件 '{csv_file_path}' 到目录 '{output_dir}'")
        converted_count = 0
        failed_count = 0
        errors = []
        question_number = 1 # 初始化题目序号

        try:
            csv_data = self._read_csv(csv_file_path)
            if not csv_data:
                return {"status": "success", "message": "文件为空或未读取到任何题目数据", "converted_count": 0, "failed_count": 0}

            txt_contents = []
            adjusted_questions = [] # 新增：用于收集调整过的题目信息

            for i, row in enumerate(csv_data):
                try:
                    question_data = self._parse_question_data(row)
                    
                    # 调用题目类型调整逻辑
                    adjustment_info = self._adjust_question_type(question_data)
                    if adjustment_info:
                        adjusted_questions.append(adjustment_info)

                    # 传递题目序号
                    formatted_question = self._format_question(question_data, question_number)
                    if formatted_question:
                        txt_contents.append(formatted_question)
                        txt_contents.append("\n") # Add a newline between questions for readability
                        converted_count += 1
                        question_number += 1 # 成功格式化后递增序号
                    else:
                        failed_count += 1
                        errors.append({"row": i+1, "reason": f"无法格式化题目: {row}"})
                except Exception as e:
                    logging.error(f"处理CSV第{i+1}行时发生错误: {e}. 行数据: {row}")
                    failed_count += 1
                    errors.append({"row": i+1, "reason": str(e), "data": row})
                    continue

            output_file_name = os.path.basename(csv_file_path).replace('.csv', '.txt')
            output_file_path = os.path.join(output_dir, output_file_name)
            self._write_txt("\n".join(txt_contents), output_file_path)

            logging.info(f"CSV文件 '{csv_file_path}' 成功转换为TXT文件 '{output_file_path}'")
            return {"status": "success", "output_path": output_file_path, "converted_count": converted_count, "failed_count": failed_count, "errors": errors, "adjusted_questions": adjusted_questions}

        except FileNotFoundError:
            error_msg = f"文件不存在: {csv_file_path}"
            logging.error(error_msg)
            return {"status": "error", "message": error_msg, "converted_count": converted_count, "failed_count": failed_count, "errors": errors, "adjusted_questions": []}
        except Exception as e:
            logging.error(f"转换过程中发生致命错误: {e}")
            return {"status": "error", "message": str(e), "converted_count": converted_count, "failed_count": failed_count, "errors": errors, "adjusted_questions": []} 