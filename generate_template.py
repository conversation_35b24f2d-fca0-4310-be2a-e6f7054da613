import csv

def generate_question_template(input_csv_path, output_csv_path):
    with open(input_csv_path, 'r', encoding='utf-8') as infile, \
         open(output_csv_path, 'w', encoding='utf-8', newline='') as outfile:
        reader = csv.reader(infile)
        writer = csv.writer(outfile)

        # Write header for the output CSV
        writer.writerow(['*题型', '*题目', '*A', '*B', 'C', 'D', 'E', 'F', '*答案', '*难度', '试题解析'])

        # Skip header of input CSV
        next(reader)

        for row in reader:
            if not row:
                continue

            # Extract data from input_csv_path
            question_body = row[0]  # 试题题干(必填)
            question_type = row[1]  # 试题类型(必填，题型请用下拉菜单实现）
            options_str = row[2]    # 选项（用'|'隔开）
            answer = row[3]         # 答案（填空题用'|'隔开）
            difficulty = row[5]     # 难易度 (必填，难易度请选择下拉菜单实现)
            analysis = row[6]       # 试题解析

            # Parse options
            options = options_str.split('|') if options_str else []
            
            # Pad options to 6, if less than 6
            padded_options = options + [''] * (6 - len(options))

            # Map question type
            mapped_question_type = ''
            if question_type == '单选':
                mapped_question_type = '单选题'
            elif question_type == '多选':
                mapped_question_type = '多选题'
            elif question_type == '判断':
                mapped_question_type = '判断题'
            else:
                # If the question type is not '单选', '多选', or '判断', skip this row
                continue

            # Write data to output CSV
            output_row = [
                mapped_question_type,
                question_body,
                padded_options[0] if len(padded_options) > 0 else '',
                padded_options[1] if len(padded_options) > 1 else '',
                padded_options[2] if len(padded_options) > 2 else '',
                padded_options[3] if len(padded_options) > 3 else '',
                padded_options[4] if len(padded_options) > 4 else '',
                padded_options[5] if len(padded_options) > 5 else '',
                answer,
                difficulty,
                analysis
            ]
            writer.writerow(output_row)

# Run the generation
input_file = 'output/quiz_results.csv'
output_file = 'output/客观题导入模板_utf8.csv'
generate_question_template(input_file, output_file)

print(f"'{output_file}' has been generated successfully.") 