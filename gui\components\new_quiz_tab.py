from tkinter import Frame, Label, Button, Entry, filedialog, Text, END, messagebox # Added messagebox
import os # Import os module for path operations
from csv_to_tdocx.conversion_manager import ConversionManager # Import ConversionManager

class NewTabWidget(Frame):
    def __init__(self, master=None):
        super().__init__(master, padx=10, pady=10)
        self.grid(row=0, column=0, sticky="nsew")
        self.master.grid_rowconfigure(0, weight=1)
        self.master.grid_columnconfigure(0, weight=1)
        self.conversion_manager = ConversionManager() # Initialize ConversionManager
        self.create_widgets()

    def create_widgets(self):
        # CSV File Selection
        Label(self, text="1. 选择CSV文件：").grid(row=0, column=0, sticky="w", pady=5)
        self.csv_path_entry = Entry(self, width=50)
        self.csv_path_entry.grid(row=0, column=1, pady=5, padx=5)
        Button(self, text="浏览...", command=self._browse_csv_file).grid(row=0, column=2, pady=5)

        # Output Directory Selection
        Label(self, text="2. 选择输出目录：").grid(row=1, column=0, sticky="w", pady=5)
        self.output_dir_entry = Entry(self, width=50)
        self.output_dir_entry.grid(row=1, column=1, pady=5, padx=5)
        Button(self, text="浏览...", command=self._browse_output_dir).grid(row=1, column=2, pady=5)

        # Output File Name Input
        Label(self, text="3. 输入输出文件名 (例如: output.docx)：").grid(row=2, column=0, sticky="w", pady=5)
        self.output_filename_entry = Entry(self, width=50)
        self.output_filename_entry.grid(row=2, column=1, pady=5, padx=5)

        # Start Conversion Button
        self.convert_button = Button(self, text="开始转换", command=self._start_conversion)
        self.convert_button.grid(row=3, column=1, pady=20)

        # Status display area
        Label(self, text="状态：").grid(row=4, column=0, sticky="nw", pady=5)
        self.status_text = Text(self, height=10, width=70, state='disabled') # Set initial state to disabled
        self.status_text.grid(row=4, column=1, columnspan=2, pady=5, padx=5)

    def _browse_csv_file(self):
        file_path = filedialog.askopenfilename(filetypes=[("CSV Files", "*.csv")])
        if file_path:
            self.csv_path_entry.delete(0, "end")
            self.csv_path_entry.insert(0, file_path)

    def _browse_output_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.output_dir_entry.delete(0, "end")
            self.output_dir_entry.insert(0, dir_path)

    def _update_status(self, message):
        self.status_text.config(state='normal') # Enable text widget to insert text
        self.status_text.insert(END, message + "\n")
        self.status_text.see(END) # Scroll to the end
        self.status_text.config(state='disabled') # Disable text widget after inserting

    def _start_conversion(self):
        self._update_status("正在开始转换...")
        csv_file_path = self.csv_path_entry.get()
        output_dir = self.output_dir_entry.get()
        output_filename = self.output_filename_entry.get().strip()
        template_file_path = "新模板.ini" # Assuming template file is in root directory

        if not csv_file_path or not output_dir or not output_filename:
            error_message = "错误：请填写所有必填项（CSV文件、输出目录、输出文件名）。"
            self._update_status(error_message)
            messagebox.showerror("输入错误", error_message) # Display error pop-up
            return

        # 确保输出文件名包含 .docx 扩展名
        if not output_filename.lower().endswith(".docx"):
            output_filename += ".docx"

        output_file_full_path = os.path.join(output_dir, output_filename)

        try:
            self.conversion_manager.convert_csv_to_tdocx(
                csv_file_path,
                template_file_path,
                output_file_full_path
            )
            success_message = f"转换成功！文件已保存到：{output_file_full_path}"
            self._update_status(success_message)
            messagebox.showinfo("转换成功", success_message) # Display success pop-up
        except FileNotFoundError as e:
            error_message = f"错误：{e}"
            self._update_status(error_message)
            messagebox.showerror("文件未找到错误", error_message) # Display specific error pop-up
        except Exception as e:
            error_message = f"转换失败：{e}"
            self._update_status(error_message)
            messagebox.showerror("转换错误", error_message) # Display general error pop-up 