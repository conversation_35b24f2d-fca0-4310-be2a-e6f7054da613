import tkinter as tk
from tkinter import ttk, messagebox

class ProcessingConfigPanel(ttk.LabelFrame):
    def __init__(self, parent, max_chunk_size_var, question_base_chars_var, disable_splitting_var,
                 use_new_splitting_var, single_choice_var, multiple_choice_var,
                 fill_blank_var, short_answer_var, true_false_var, sorting_var, *args, **kwargs):
        super().__init__(parent, *args, **kwargs)
        self.max_chunk_size_var = max_chunk_size_var
        self.question_base_chars_var = question_base_chars_var
        self.disable_splitting_var = disable_splitting_var
        self.use_new_splitting_var = use_new_splitting_var
        self.single_choice_var = single_choice_var
        self.multiple_choice_var = multiple_choice_var
        self.fill_blank_var = fill_blank_var
        self.short_answer_var = short_answer_var
        self.true_false_var = true_false_var
        self.sorting_var = sorting_var
        self.create_processing_config_frame()

    def create_processing_config_frame(self):
        """创建文本处理配置框架（在设置标签页中）"""
        proc_frame = ttk.LabelFrame(self, text="文本处理配置", padding="5")
        proc_frame.grid(row=1, column=0, columnspan=1, sticky=(tk.W, tk.E), pady=5)

        # 文本块大小
        ttk.Label(proc_frame, text="文本块大小 (tokens):").grid(row=0, column=0, sticky=tk.W, pady=5)
        chunk_size_frame = ttk.Frame(proc_frame)
        chunk_size_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        chunk_scale = ttk.Scale(chunk_size_frame, from_=500, to=8000, variable=self.max_chunk_size_var,
                               orient=tk.HORIZONTAL, length=200)
        chunk_scale.pack(side=tk.LEFT)
        chunk_scale.configure(command=lambda v: self.max_chunk_size_var.set(int(float(v))))
        ttk.Label(chunk_size_frame, textvariable=self.max_chunk_size_var).pack(side=tk.LEFT, padx=5)
        # 添加文本块大小说明
        ttk.Label(proc_frame, text="每个文本块的最大token数，影响处理速度和质量。", font=("Arial", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)

        # 题目基础字数设置
        ttk.Label(proc_frame, text="题目基础字数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        base_chars_frame = ttk.Frame(proc_frame)
        base_chars_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        base_chars_scale = ttk.Scale(base_chars_frame, from_=500, to=5000, variable=self.question_base_chars_var,
                                    orient=tk.HORIZONTAL, length=200)
        base_chars_scale.pack(side=tk.LEFT)
        base_chars_scale.configure(command=lambda v: self.question_base_chars_var.set(int(float(v))))
        ttk.Label(base_chars_frame, textvariable=self.question_base_chars_var).pack(side=tk.LEFT, padx=5)
        # 添加题目基础字数说明
        ttk.Label(proc_frame, text="生成一组题目的基础字数。", font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)


        # 文档分割选项（移至第二列）
        ttk.Label(proc_frame, text="文档分割:").grid(row=0, column=3, sticky=tk.W, pady=5)
        splitting_options_frame = ttk.Frame(proc_frame)
        splitting_options_frame.grid(row=0, column=4, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)

        ttk.Checkbutton(splitting_options_frame, text="不进行文档分割",
                       variable=self.disable_splitting_var).pack(side=tk.LEFT)
        ttk.Label(splitting_options_frame, text="勾选后将对整个文档生成题目，忽略分块设置。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 新分块逻辑选项（移至第二列）
        ttk.Label(proc_frame, text="分块逻辑:").grid(row=1, column=3, sticky=tk.W, pady=5)
        new_logic_options_frame = ttk.Frame(proc_frame)
        new_logic_options_frame.grid(row=1, column=4, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5)

        ttk.Checkbutton(new_logic_options_frame, text="使用新分块逻辑",
                       variable=self.use_new_splitting_var).pack(side=tk.LEFT)
        ttk.Label(new_logic_options_frame, text="采用更智能的分段方式，通常生成无重叠的文本块，并根据字数进行调整。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 预设配置按钮
        preset_frame = ttk.Frame(proc_frame)
        preset_frame.grid(row=5, column=0, columnspan=3, pady=10)

        ttk.Button(preset_frame, text="快速配置", command=self.apply_fast_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="平衡配置", command=self.apply_balanced_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="精细配置", command=self.apply_detailed_preset).pack(side=tk.LEFT, padx=5)

        proc_frame.columnconfigure(1, weight=1)

    def apply_fast_preset(self):
        """应用快速配置预设"""
        self.max_chunk_size_var.set(1500)
        self.question_base_chars_var.set(1500)
        # 快速配置：较少题目
        self.single_choice_var.set(1)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(0)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        total_questions = self.single_choice_var.get() + self.multiple_choice_var.get() + self.fill_blank_var.get() + self.short_answer_var.get() + self.true_false_var.get() + self.sorting_var.get()
        messagebox.showinfo("配置应用", f"已应用快速配置：\n• 文本块大小: 1500 字符\n• 题目基础字数: 1500\n• 总题目数量: {total_questions}\n• 题型: 单选1+多选1+填空1+判断1")

    def apply_balanced_preset(self):
        """应用平衡配置预设"""
        self.max_chunk_size_var.set(2000)
        self.question_base_chars_var.set(2000)
        # 平衡配置：中等题目数量
        self.single_choice_var.set(2)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        total_questions = self.single_choice_var.get() + self.multiple_choice_var.get() + self.fill_blank_var.get() + self.short_answer_var.get() + self.true_false_var.get() + self.sorting_var.get()
        messagebox.showinfo("配置应用", f"已应用平衡配置：\n• 文本块大小: 2000 字符\n• 题目基础字数: 2000\n• 总题目数量: {total_questions}\n• 题型: 单选2+多选1+填空1+简答1+判断1")

    def apply_detailed_preset(self):
        """应用精细配置预设"""
        self.max_chunk_size_var.set(5000)
        self.question_base_chars_var.set(1300)
        # 精细配置：较多题目
        self.single_choice_var.set(4)
        self.multiple_choice_var.set(2)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(2)
        self.sorting_var.set(0)
        total_questions = self.single_choice_var.get() + self.multiple_choice_var.get() + self.fill_blank_var.get() + self.short_answer_var.get() + self.true_false_var.get() + self.sorting_var.get()
        messagebox.showinfo("配置应用", f"已应用精细配置：\n• 文本块大小: 5000 字符\n• 题目基础字数: 1300\n• 总题目数量: {total_questions}\n• 题型: 单选4+多选2+填空1+简答1+判断2+排序0")