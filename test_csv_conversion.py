import os
import sys
import pandas as pd

# Assuming the current working directory is the project root
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'quiz_converter'))

from quiz_formatter import QuizFormatConverter

source_file = 'output/quiz_results.csv'
target_file = 'output/客观题导入模板_utf8.csv'

# Ensure output directory exists
output_dir = os.path.dirname(target_file)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print(f"尝试将 {source_file} 转换为 {target_file}")

if not os.path.exists(source_file):
    print(f"错误：源文件 {source_file} 不存在。请确保该文件存在。")
else:
    try:
        converter = QuizFormatConverter(source_file, target_file)
        result = converter.convert()
        if result.get("success"):
            print(f"转换成功！已转换 {result.get('converted_count')} 行，跳过 {result.get('skipped_count')} 行。文件已保存到 {target_file}")
        else:
            print(f"转换失败：{result.get('message')}. 错误详情：{result.get('errors')}")
    except Exception as e:
        print(f"在转换过程中发生异常：{e}") 