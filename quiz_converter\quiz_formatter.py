import pandas as pd
import codecs

class QuizFormatConverter:
    OUTPUT_COLUMNS = ['*题型', '*题目', '*A', '*B', 'C', 'D', 'E', 'F', '*答案', '*难度', '试题解析']

    def __init__(self, source_file: str, target_file: str):
        self.source_file = source_file
        self.target_file = target_file 

    def _read_csv(self) -> pd.DataFrame:
        try:
            # Try reading with utf-8-sig to handle BOM
            df = pd.read_csv(self.source_file, encoding='utf-8-sig')
        except UnicodeDecodeError:
            # Fallback to utf-8 if utf-8-sig fails
            df = pd.read_csv(self.source_file, encoding='utf-8')
        return df 

    def _process_row(self, row: dict) -> dict | None:
        # Initialize output_row with all expected columns and empty strings
        output_row = {
            '*题型': '',
            '*题目': '',
            '*A': '',
            '*B': '',
            'C': '',
            'D': '',
            'E': '',
            'F': '',
            '*答案': '',
            '*难度': '',
            '试题解析': ''
        }

        # Extract data from input_csv_path
        question_body = row.get('试题题干(必填)', '')
        question_type = row.get('试题类型(必填，题型请用下拉菜单实现）', '')
        options_str = row.get('选项（用\'|\'隔开）', '')
        answer = row.get('答案（填空题用\'|\'隔开）(必填)', '')
        difficulty = row.get('难易度 (必填，难易度请选择下拉菜单实现)', '')
        analysis = row.get('试题解析', '')

        # Skip specified quiz types
        if question_type in ['填空', '问答', '排序']:
            return None

        # Map question type
        mapped_question_type = ''
        if question_type.endswith('题'):
            mapped_question_type = question_type[:-1]  # Remove last character if ends with '题'
        else:
            mapped_question_type = question_type

        # Assign values to the pre-initialized output_row
        output_row['*题型'] = mapped_question_type
        output_row['*题目'] = question_body
        output_row['试题解析'] = analysis
        output_row['*难度'] = self._convert_difficulty(difficulty)
        
        # Convert answer for different question types
        if mapped_question_type == '判断':
            # Standardize judgment answers to A/B format
            true_answers = ['对', '正确', 'True', 'true', 'T', 't', '是']
            if str(answer).strip() in true_answers:
                output_row['*答案'] = 'A'
            else:
                output_row['*答案'] = 'B'
        else:
            output_row['*答案'] = answer

        # Handle options
        options_data = self._handle_options(options_str, question_type)
        output_row.update(options_data) # This will fill in the option values

        return output_row

    # Placeholder for P1.5
    def _convert_difficulty(self, difficulty: str) -> str:
        difficulty_map = {
            '低': '简单',
            '中': '中等',
            '高': '困难'
        }
        return difficulty_map.get(difficulty, '')

    # Placeholder for P1.6
    def _handle_options(self, options_str: str, question_type: str) -> dict:
        options_data = {
            '*A': '',
            '*B': '',
            'C': '',
            'D': '',
            'E': '',
            'F': ''
        }

        if question_type in ['单选', '多选']:
            options = [opt.strip() for opt in options_str.split('|') if opt.strip()]
            option_keys = ['*A', '*B', 'C', 'D', 'E', 'F']
            for i, opt in enumerate(options):
                if i < len(option_keys):
                    options_data[option_keys[i]] = opt
        elif question_type == '判断':
            options_data['*A'] = '正确'
            options_data['*B'] = '错误'
        
        return options_data 

    def _write_csv(self, df: pd.DataFrame):
        # Write CSV header with 2 lines:
        # 1. Instructions line (kept as is)
        # 2. Column headers line
        instructions = '1.表头行带*号的列必须填写。2.客观题题型必须是单选、多选、判断。3.单选题和多选题的选项数量至少填写三个并且从A选项依次向后填写。4.判断题的选项只有两个，A正确,B错误。5.题目的题干、选项和试题解析中均可上传图片，图片为jpg格式或者png格式，图片规范命名{图片名称.jpg}、{图片名称.png}。图片均放在（图片文件夹）中。6.导入题库时，若存在图片文件，请将此表与图片文件夹压缩为zip压缩文件，进行上传。7.当前说明行不可删除，否则第一条试题数据无法导入。\n'
        column_headers = ','.join(self.OUTPUT_COLUMNS) + '\n'

        with open(self.target_file, 'w', encoding='utf-8-sig') as f:
            f.write(instructions)
            f.write(column_headers)
            
        # Append the data (starting from line 3)
        df.to_csv(self.target_file, index=False, encoding='utf-8-sig',
                 columns=self.OUTPUT_COLUMNS, mode='a', header=False)

    def convert(self) -> dict:
        converted_count = 0
        skipped_count = 0
        
        try:
            df = self._read_csv()
            processed_rows = []
            
            for _, row in df.iterrows():
                processed_row = self._process_row(row.to_dict())
                if processed_row:
                    processed_rows.append(processed_row)
                    converted_count += 1
                else:
                    skipped_count += 1
            
            output_columns_for_df = self.OUTPUT_COLUMNS
            if not processed_rows:
                output_df = pd.DataFrame(columns=output_columns_for_df)
            else:
                output_df = pd.DataFrame(processed_rows, columns=output_columns_for_df)
            self._write_csv(output_df)
            
            return {
                "success": True,
                "converted_count": converted_count,
                "skipped_count": skipped_count,
                "message": "转换完成。"
            }
        except Exception as e:
            return {
                "success": False,
                "converted_count": converted_count,
                "skipped_count": skipped_count,
                "message": f"转换失败: {str(e)}"
            } 