import pandas as pd
import codecs

class QuizFormatConverter:
    OUTPUT_COLUMNS = ['*题型', '*题目', '*A', '*B', 'C', 'D', 'E', 'F', '*答案', '*难度', '试题解析']

    def __init__(self, source_file: str, target_file: str):
        self.source_file = source_file
        self.target_file = target_file 

    def _read_csv(self) -> pd.DataFrame:
        try:
            # Try reading with utf-8-sig to handle BOM
            df = pd.read_csv(self.source_file, encoding='utf-8-sig')
        except UnicodeDecodeError:
            # Fallback to utf-8 if utf-8-sig fails
            df = pd.read_csv(self.source_file, encoding='utf-8')
        return df 

    def _process_row(self, row: dict) -> dict | None:
        # Initialize output_row with all expected columns and empty strings
        output_row = {
            '*题型': '',
            '*题目': '',
            '*A': '',
            '*B': '',
            'C': '',
            'D': '',
            'E': '',
            'F': '',
            '*答案': '',
            '*难度': '',
            '试题解析': ''
        }

        # Extract data from input_csv_path
        question_body = row.get('试题题干(必填)', '')
        question_type = row.get('试题类型(必填，题型请用下拉菜单实现）', '')
        options_str = row.get('选项（用\'|\'隔开）', '')
        answer = row.get('答案（填空题用\'|\'隔开）(必填)', '')
        difficulty = row.get('难易度 (必填，难易度请选择下拉菜单实现)', '')
        analysis = row.get('试题解析', '')

        # Skip specified quiz types
        if question_type in ['填空', '问答', '排序']:
            return None

        # Map question type
        mapped_question_type = ''
        if question_type == '单选':
            mapped_question_type = '单选题'
        elif question_type == '多选':
            mapped_question_type = '多选题'
        elif question_type == '判断':
            mapped_question_type = '判断题'
        else:
            # If the question type is not '单选', '多选', or '判断', skip this row
            return None

        # Assign values to the pre-initialized output_row
        output_row['*题型'] = mapped_question_type
        output_row['*题目'] = question_body
        output_row['*答案'] = answer
        output_row['试题解析'] = analysis
        output_row['*难度'] = self._convert_difficulty(difficulty)

        # Handle options
        options_data = self._handle_options(options_str, question_type)
        output_row.update(options_data) # This will fill in the option values

        return output_row

    # Placeholder for P1.5
    def _convert_difficulty(self, difficulty: str) -> str:
        difficulty_map = {
            '低': '简单',
            '中': '中等',
            '高': '困难'
        }
        return difficulty_map.get(difficulty, '')

    # Placeholder for P1.6
    def _handle_options(self, options_str: str, question_type: str) -> dict:
        options_data = {
            '*A': '',
            '*B': '',
            'C': '',
            'D': '',
            'E': '',
            'F': ''
        }

        if question_type in ['单选', '多选']:
            options = [opt.strip() for opt in options_str.split('|') if opt.strip()]
            option_keys = ['*A', '*B', 'C', 'D', 'E', 'F']
            for i, opt in enumerate(options):
                if i < len(option_keys):
                    options_data[option_keys[i]] = opt
        elif question_type == '判断':
            options_data['*A'] = '正确'
            options_data['*B'] = '错误'
        
        return options_data 

    def _write_csv(self, df: pd.DataFrame):
        df.to_csv(self.target_file, index=False, encoding='utf-8-sig', columns=self.OUTPUT_COLUMNS) 

    def convert(self) -> dict:
        converted_count = 0
        skipped_count = 0
        
        try:
            df = self._read_csv()
            processed_rows = []
            
            for _, row in df.iterrows():
                processed_row = self._process_row(row.to_dict())
                if processed_row:
                    processed_rows.append(processed_row)
                    converted_count += 1
                else:
                    skipped_count += 1
            
            output_columns_for_df = self.OUTPUT_COLUMNS
            if not processed_rows:
                output_df = pd.DataFrame(columns=output_columns_for_df)
            else:
                output_df = pd.DataFrame(processed_rows, columns=output_columns_for_df)
            self._write_csv(output_df)
            
            return {
                "success": True,
                "converted_count": converted_count,
                "skipped_count": skipped_count,
                "message": "转换完成。"
            }
        except Exception as e:
            return {
                "success": False,
                "converted_count": converted_count,
                "skipped_count": skipped_count,
                "message": f"转换失败: {str(e)}"
            } 