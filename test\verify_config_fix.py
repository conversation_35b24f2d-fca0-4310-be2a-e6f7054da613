#!/usr/bin/env python3
"""
验证题型配置修复的脚本
模拟实际使用场景
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import Config
from utils.question_calculator import QuestionCalculator

def test_real_world_scenario():
    """测试真实世界场景"""
    print("=== 真实世界场景测试 ===")
    
    # 1. 创建配置并保存
    print("1. 创建并保存配置...")
    config = Config()
    config.SINGLE_CHOICE_COUNT = 3
    config.MULTIPLE_CHOICE_COUNT = 2
    config.FILL_BLANK_COUNT = 1
    config.SHORT_ANSWER_COUNT = 1
    config.TRUE_FALSE_COUNT = 2
    config.SORTING_COUNT = 0
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        temp_config_path = f.name
        config.save_to_json_file(temp_config_path)
    
    print(f"配置已保存到: {temp_config_path}")
    print(f"保存的配置: 单选{config.SINGLE_CHOICE_COUNT}, 多选{config.MULTIPLE_CHOICE_COUNT}, "
          f"填空{config.FILL_BLANK_COUNT}, 简答{config.SHORT_ANSWER_COUNT}, "
          f"判断{config.TRUE_FALSE_COUNT}, 排序{config.SORTING_COUNT}")
    
    try:
        # 2. 重新加载配置
        print("\n2. 重新加载配置...")
        loaded_config = Config.from_json_file(temp_config_path)
        
        print(f"加载的配置: 单选{loaded_config.SINGLE_CHOICE_COUNT}, 多选{loaded_config.MULTIPLE_CHOICE_COUNT}, "
              f"填空{loaded_config.FILL_BLANK_COUNT}, 简答{loaded_config.SHORT_ANSWER_COUNT}, "
              f"判断{loaded_config.TRUE_FALSE_COUNT}, 排序{loaded_config.SORTING_COUNT}")
        
        # 3. 验证配置一致性
        print("\n3. 验证配置一致性...")
        assert loaded_config.SINGLE_CHOICE_COUNT == 3, f"单选题配置不一致: {loaded_config.SINGLE_CHOICE_COUNT}"
        assert loaded_config.MULTIPLE_CHOICE_COUNT == 2, f"多选题配置不一致: {loaded_config.MULTIPLE_CHOICE_COUNT}"
        assert loaded_config.FILL_BLANK_COUNT == 1, f"填空题配置不一致: {loaded_config.FILL_BLANK_COUNT}"
        assert loaded_config.SHORT_ANSWER_COUNT == 1, f"简答题配置不一致: {loaded_config.SHORT_ANSWER_COUNT}"
        assert loaded_config.TRUE_FALSE_COUNT == 2, f"判断题配置不一致: {loaded_config.TRUE_FALSE_COUNT}"
        assert loaded_config.SORTING_COUNT == 0, f"排序题配置不一致: {loaded_config.SORTING_COUNT}"
        
        print("配置一致性验证通过")
        
        # 4. 测试题目计算器
        print("\n4. 测试题目计算器...")
        calculator = QuestionCalculator(loaded_config)
        
        # 测试不同字符数的计算
        test_cases = [
            (1000, "小文本"),
            (2000, "标准文本"),
            (4000, "大文本")
        ]
        
        for char_count, description in test_cases:
            result = calculator.calculate_question_counts(char_count)
            total = sum(result.values())
            print(f"{description}({char_count}字符): 总题目{total}道")
            print(f"  详细分布: {result}")
            
            # 验证基础配置是否正确应用
            if char_count <= 2000:  # 小于等于阈值时应该使用基础配置
                assert result['single_choice_count'] == 3, f"单选题数量错误: {result['single_choice_count']}"
                assert result['multiple_choice_count'] == 2, f"多选题数量错误: {result['multiple_choice_count']}"
                assert result['fill_blank_count'] == 1, f"填空题数量错误: {result['fill_blank_count']}"
                assert result['short_answer_count'] == 1, f"简答题数量错误: {result['short_answer_count']}"
                assert result['true_false_count'] == 2, f"判断题数量错误: {result['true_false_count']}"
                assert result['sorting_count'] == 0, f"排序题数量错误: {result['sorting_count']}"
        
        print("题目计算器测试通过")
        
        # 5. 测试配置更新
        print("\n5. 测试配置更新...")
        new_config = Config()
        new_config.SINGLE_CHOICE_COUNT = 5
        new_config.MULTIPLE_CHOICE_COUNT = 3
        new_config.FILL_BLANK_COUNT = 2
        new_config.SHORT_ANSWER_COUNT = 1
        new_config.TRUE_FALSE_COUNT = 3
        new_config.SORTING_COUNT = 1
        
        calculator.update_config(new_config)
        
        # 验证更新后的计算
        result = calculator.calculate_question_counts(2000)
        print(f"更新配置后的计算结果: {result}")
        
        assert result['single_choice_count'] == 5, f"更新后单选题数量错误: {result['single_choice_count']}"
        assert result['multiple_choice_count'] == 3, f"更新后多选题数量错误: {result['multiple_choice_count']}"
        assert result['fill_blank_count'] == 2, f"更新后填空题数量错误: {result['fill_blank_count']}"
        assert result['short_answer_count'] == 1, f"更新后简答题数量错误: {result['short_answer_count']}"
        assert result['true_false_count'] == 3, f"更新后判断题数量错误: {result['true_false_count']}"
        assert result['sorting_count'] == 1, f"更新后排序题数量错误: {result['sorting_count']}"
        
        print("配置更新测试通过")
        
        print("\n所有真实世界场景测试通过！")
        
    finally:
        # 清理临时文件
        os.unlink(temp_config_path)

def test_config_file_format():
    """测试配置文件格式"""
    print("\n=== 配置文件格式测试 ===")
    
    # 创建配置
    config = Config()
    config.SINGLE_CHOICE_COUNT = 4
    config.MULTIPLE_CHOICE_COUNT = 2
    config.FILL_BLANK_COUNT = 2
    config.SHORT_ANSWER_COUNT = 1
    config.TRUE_FALSE_COUNT = 2
    config.SORTING_COUNT = 1
    
    # 转换为字典
    config_dict = config.to_dict()
    
    # 验证字典结构
    assert 'processing' in config_dict, "配置字典缺少processing部分"
    processing = config_dict['processing']
    
    assert 'single_choice_count' in processing, "配置字典缺少single_choice_count"
    assert 'multiple_choice_count' in processing, "配置字典缺少multiple_choice_count"
    assert 'fill_blank_count' in processing, "配置字典缺少fill_blank_count"
    assert 'short_answer_count' in processing, "配置字典缺少short_answer_count"
    assert 'true_false_count' in processing, "配置字典缺少true_false_count"
    assert 'sorting_count' in processing, "配置字典缺少sorting_count"
    
    # 验证值
    assert processing['single_choice_count'] == 4, f"单选题配置值错误: {processing['single_choice_count']}"
    assert processing['multiple_choice_count'] == 2, f"多选题配置值错误: {processing['multiple_choice_count']}"
    assert processing['fill_blank_count'] == 2, f"填空题配置值错误: {processing['fill_blank_count']}"
    assert processing['short_answer_count'] == 1, f"简答题配置值错误: {processing['short_answer_count']}"
    assert processing['true_false_count'] == 2, f"判断题配置值错误: {processing['true_false_count']}"
    assert processing['sorting_count'] == 1, f"排序题配置值错误: {processing['sorting_count']}"
    
    print("配置文件格式测试通过")

if __name__ == "__main__":
    print("开始验证题型配置修复...")
    
    try:
        test_real_world_scenario()
        test_config_file_format()
        
        print("\n验证完成！题型配置修复成功，可以正常使用。")
        
    except Exception as e:
        print(f"\n验证失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
