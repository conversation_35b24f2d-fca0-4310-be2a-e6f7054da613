import os
import sys
import logging
import json
import queue
from typing import List, Dict, Any
import tkinter as tk
from tkinter import ttk, messagebox, filedialog

from config import Config
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from utils.helpers import scan_directory_structure
from gui.components.file_selection_panel import FileSelectionPanel
from gui.components.output_config_panel import OutputConfigPanel
from gui.components.api_config_panel import APIConfigPanel
from gui.components.processing_config_panel import ProcessingConfigPanel
from gui.components.question_types_config_panel import QuestionTypesConfigPanel
from gui.components.question_display_panel import QuestionDisplayPanel
from gui.managers.config_manager import ConfigManager
from gui.managers.processing_manager import ProcessingManager
from csv_to_tdocx.csv_to_txt_converter import QuizToTxtConverter
from csv_to_tdocx.csv_to_docx_converter import QuizToDocxConverter
from quiz_converter.quiz_formatter import QuizFormatConverter

logger = logging.getLogger(__name__)

class MainApplication:
    def __init__(self, root):
        self.root = root
        self.root.title("文件处理工具")
        self.root.geometry("1400x800")  # 增大窗口以容纳右侧显示区域

        # 初始化线程相关变量
        self.question_queue = queue.Queue()

        # 初始化所有GUI变量
        self.init_gui_variables()

        # 初始化ConfigManager (OutputConfigPanel依赖它)
        self.config_manager = ConfigManager()

        # 创建主框架和标签页 (这将创建GUI组件，包括self.question_display和self.progress_bar)
        self.create_notebook()

        # 初始化ProcessingManager (现在GUI组件已可用)
        self.processing_manager = ProcessingManager(
            self.root,
            self.gui_vars,
            self.status_var,
            self.question_queue,
            self.progress_var,
            self.progress_bar,
            self.question_display
        )

        # 加载配置
        self.load_initial_config()

        # 设置状态
        self.status_var.set("就绪")

        # 启动队列监听
        self.start_queue_monitoring()

    def init_gui_variables(self):
        """初始化所有GUI变量"""
        self.gui_vars = {}

        # 文件选择相关变量
        self.file_path_var = tk.StringVar()
        self.recursive_var = tk.BooleanVar(value=True)
        self.enable_docx_var = tk.BooleanVar(value=True)
        self.enable_md_var = tk.BooleanVar(value=True)
        self.enable_txt_var = tk.BooleanVar(value=True)
        self.enable_pdf_var = tk.BooleanVar(value=True)

        # 输出配置相关变量
        self.csv_path_var = tk.StringVar()
        self.csv_name_var = tk.StringVar(value='output.csv')

        # API配置相关变量
        self.api_base_var = tk.StringVar()
        self.api_key_var = tk.StringVar()
        self.model_name_var = tk.StringVar(value='gpt-3.5-turbo')
        self.temperature_var = tk.DoubleVar(value=0.7)
        self.max_tokens_var = tk.IntVar(value=2000)

        # 处理配置相关变量
        self.max_chunk_size_var = tk.IntVar(value=2000)
        self.questions_per_chunk_var = tk.IntVar(value=7)
        self.question_base_chars_var = tk.IntVar(value=2000)
        self.disable_splitting_var = tk.BooleanVar(value=False)
        self.use_new_splitting_var = tk.BooleanVar(value=True)
        self.enable_chunk_merging_var = tk.BooleanVar(value=True)

        # 题型数量相关变量 - 使用配置文件中的默认值
        self.single_choice_var = tk.IntVar(value=2)
        self.multiple_choice_var = tk.IntVar(value=1)
        self.fill_blank_var = tk.IntVar(value=1)
        self.short_answer_var = tk.IntVar(value=1)
        self.true_false_var = tk.IntVar(value=1)
        self.sorting_var = tk.IntVar(value=0)

        # CSV转换通用变量 (TXT和DOCX共用)
        self.conversion_input_path_var = tk.StringVar()
        self.conversion_output_dir_var = tk.StringVar()

        # 状态显示变量
        self.status_var = tk.StringVar()
        self.progress_var = tk.StringVar(value="0/0")
        self.progress_bar = None # 进度条在 create_right_question_panel 中创建
        self.question_display = None # 题目显示区域在 create_right_question_panel 中创建

        # 将所有变量添加到字典中
        self.gui_vars.update({
            'file_path_var': self.file_path_var,
            'recursive_var': self.recursive_var,
            'enable_docx_var': self.enable_docx_var,
            'enable_md_var': self.enable_md_var,
            'enable_txt_var': self.enable_txt_var,
            'enable_pdf_var': self.enable_pdf_var,
            'csv_path_var': self.csv_path_var,
            'csv_name_var': self.csv_name_var,
            'api_base_var': self.api_base_var,
            'api_key_var': self.api_key_var,
            'model_name_var': self.model_name_var,
            'temperature_var': self.temperature_var,
            'max_tokens_var': self.max_tokens_var,
            'max_chunk_size_var': self.max_chunk_size_var,
            'questions_per_chunk_var': self.questions_per_chunk_var,
            'question_base_chars_var': self.question_base_chars_var,
            'disable_splitting_var': self.disable_splitting_var,
            'use_new_splitting_var': self.use_new_splitting_var,
            'enable_chunk_merging_var': self.enable_chunk_merging_var,
            'single_choice_var': self.single_choice_var,
            'multiple_choice_var': self.multiple_choice_var,
            'fill_blank_var': self.fill_blank_var,
            'short_answer_var': self.short_answer_var,
            'true_false_var': self.true_false_var,
            'sorting_var': self.sorting_var,
            'conversion_input_path_var': self.conversion_input_path_var,
            'conversion_output_dir_var': self.conversion_output_dir_var
        })

    def init_components(self):
        """初始化GUI组件"""
        # 这些组件将在create_main_tab_widgets和create_settings_tab_widgets中创建
        pass

    def load_initial_config(self):
        """加载初始配置"""
        self.config_manager.load_config_to_gui(self.gui_vars)

    def start_queue_monitoring(self):
        """启动队列监听"""
        self.processing_manager.check_question_queue(self.add_question_to_display)

    def create_notebook(self):
        """创建标签页界面"""
        # 创建Notebook控件
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建主要操作标签页
        self.main_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.main_frame, text="主要操作")

        # 创建设置标签页
        self.settings_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.settings_frame, text="设置")

        # 创建新融智标签页
        self.new_quiz_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(self.new_quiz_frame, text="CSV转tdocx")

        # 在主要操作标签页中创建控件
        self.create_main_tab_widgets()

        # 在设置标签页中创建控件
        self.create_settings_tab_widgets()

        # 在新融智标签页中创建控件
        self.create_new_quiz_tab_widgets()

    def create_main_tab_widgets(self):
        """创建主要操作标签页的控件"""
        # 创建左右分栏
        self.create_main_paned_window()

        # 左侧控制面板
        self.create_left_control_panel()

        # 右侧题目显示区域
        self.create_right_question_panel()

    def create_main_paned_window(self):
        """创建主要操作标签页的分栏窗口"""
        # 创建PanedWindow用于左右分栏
        self.main_paned = ttk.PanedWindow(self.main_frame, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧框架（控制面板）
        self.left_frame = ttk.Frame(self.main_paned, padding="5")
        self.main_paned.add(self.left_frame, weight=1)

        # 右侧框架（题目显示）
        self.right_frame = ttk.Frame(self.main_paned, padding="5")
        self.main_paned.add(self.right_frame, weight=1)

    def create_left_control_panel(self):
        """创建左侧控制面板"""
        # 文件选择部分
        self.file_selection_panel = FileSelectionPanel(
            self.left_frame,
            self.gui_vars['file_path_var'],
            self.gui_vars['recursive_var'],
            self.gui_vars['enable_docx_var'],
            self.gui_vars['enable_md_var'],
            self.gui_vars['enable_txt_var']
        )
        self.file_selection_panel.pack(fill=tk.X, pady=5)

        # 输出配置部分
        self.output_config_panel = OutputConfigPanel(self.left_frame, self.config_manager, text="输出配置")
        self.output_config_panel.pack(fill=tk.X, pady=5)

        # 将组件的变量绑定到主应用的变量
        self.csv_path_var = self.output_config_panel.csv_path_var
        self.csv_name_var = self.output_config_panel.csv_name_var

        # 执行按钮
        self.create_execute_button()

        # 状态显示
        self.create_status_frame()

    def create_right_question_panel(self):
        """创建右侧题目显示面板"""
        self.question_display_panel = QuestionDisplayPanel(self.right_frame)
        self.question_display_panel.pack(fill=tk.BOTH, expand=True)

        # 获取组件的引用以便后续使用
        self.question_display = self.question_display_panel.question_display
        self.progress_var = self.question_display_panel.progress_var
        self.progress_bar = self.question_display_panel.progress_bar

    def create_settings_tab_widgets(self):
        """创建设置标签页的控件"""
        # API配置部分
        self.api_config_panel = APIConfigPanel(
            self.settings_frame,
            self.gui_vars['api_base_var'],
            self.gui_vars['api_key_var'],
            self.gui_vars['model_name_var'],
            self.gui_vars['temperature_var'],
            self.gui_vars['max_tokens_var'],
            text="API配置"
        )
        self.api_config_panel.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 文本处理配置部分
        self.processing_config_panel = ProcessingConfigPanel(
            self.settings_frame,
            self.gui_vars['max_chunk_size_var'],
            self.gui_vars['question_base_chars_var'],
            self.gui_vars['disable_splitting_var'],
            self.gui_vars['use_new_splitting_var'],
            self.gui_vars['single_choice_var'],
            self.gui_vars['multiple_choice_var'],
            self.gui_vars['fill_blank_var'],
            self.gui_vars['short_answer_var'],
            self.gui_vars['true_false_var'],
            self.gui_vars['sorting_var'],
            text="文本处理配置"
        )
        self.processing_config_panel.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 绑定变量
        self.max_chunk_size_var = self.processing_config_panel.max_chunk_size_var
        self.question_base_chars_var = self.processing_config_panel.question_base_chars_var
        self.disable_splitting_var = self.processing_config_panel.disable_splitting_var
        self.use_new_splitting_var = self.processing_config_panel.use_new_splitting_var
        self.single_choice_var = self.processing_config_panel.single_choice_var
        self.multiple_choice_var = self.processing_config_panel.multiple_choice_var
        self.fill_blank_var = self.processing_config_panel.fill_blank_var
        self.short_answer_var = self.processing_config_panel.short_answer_var
        self.true_false_var = self.processing_config_panel.true_false_var
        self.sorting_var = self.processing_config_panel.sorting_var

        # 题型数量配置部分 - 传递已创建的变量给面板
        self.question_types_config_panel = QuestionTypesConfigPanel(
            self.settings_frame,
            text="题型数量配置",
            single_choice_var=self.single_choice_var,
            multiple_choice_var=self.multiple_choice_var,
            fill_blank_var=self.fill_blank_var,
            short_answer_var=self.short_answer_var,
            true_false_var=self.true_false_var,
            sorting_var=self.sorting_var
        )
        self.question_types_config_panel.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 保存设置按钮
        self.create_save_settings_button()

        # 配置列权重
        self.settings_frame.columnconfigure(0, weight=1)

    def create_new_quiz_tab_widgets(self):
        """创建新融智标签页的控件 (CSV转TXT/DOCX)"""
        new_quiz_frame = self.new_quiz_frame

        # 输入CSV文件选择
        input_frame = ttk.Frame(new_quiz_frame)
        input_frame.pack(fill=tk.X, padx=10, pady=5)
        ttk.Label(input_frame, text="CSV输入文件:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(input_frame, textvariable=self.conversion_input_path_var, state='readonly').pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="浏览", command=self._browse_csv_file_for_converter).pack(side=tk.RIGHT)

        # 输出目录选择
        output_frame = ttk.Frame(new_quiz_frame)
        output_frame.pack(fill=tk.X, padx=10, pady=5)
        ttk.Label(output_frame, text="输出目录:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Entry(output_frame, textvariable=self.conversion_output_dir_var, state='readonly').pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="浏览", command=self._browse_output_dir_for_converter).pack(side=tk.RIGHT)

        # 转换按钮框架
        button_frame = ttk.Frame(new_quiz_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # CSV转TXT按钮
        convert_txt_button = ttk.Button(button_frame, text="转换为TXT", command=self._on_convert_csv_to_txt_button_click)
        convert_txt_button.pack(side=tk.LEFT, expand=True, padx=(0, 5))

        # CSV转DOCX按钮
        convert_docx_button = ttk.Button(button_frame, text="转换为DOCX", command=self._on_convert_csv_to_docx_button_click)
        convert_docx_button.pack(side=tk.LEFT, expand=True, padx=(5, 0))

        # CSV转新CSV按钮
        convert_new_csv_button = ttk.Button(button_frame, text="转换为新CSV", command=self._on_convert_csv_to_new_csv_button_click)
        convert_new_csv_button.pack(side=tk.LEFT, expand=True, padx=(5, 0))

    def _browse_csv_file_for_converter(self):
        file_path = filedialog.askopenfilename(filetypes=[("CSV Files", "*.csv")])
        if file_path:
            self.conversion_input_path_var.set(file_path)

    def _browse_output_dir_for_converter(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.conversion_output_dir_var.set(dir_path)

    def _on_convert_csv_to_txt_button_click(self):
        csv_file_path = self.conversion_input_path_var.get()
        output_dir = self.conversion_output_dir_var.get()

        if not csv_file_path or not output_dir:
            messagebox.showwarning("警告", "请选择CSV输入文件和输出目录！")
            return

        try:
            converter = QuizToTxtConverter()
            # 调用转换器的convert方法，现在它会返回调整过的题目信息
            result = converter.convert(csv_file_path, output_dir)

            if result.get("status") == "success":
                adjusted_questions = result.get("adjusted_questions", [])
                if adjusted_questions: # 如果有题目被调整
                    adjustment_details = "以下题目类型已根据答案数量自动调整：\n\n"
                    for adj in adjusted_questions:
                        adjustment_details += f"题目：{adj['question'][:50]}...\n"
                        adjustment_details += f"  原始类型：{adj['original_type']} -> 调整后类型：{adj['adjusted_type']}\n"
                        adjustment_details += f"  答案：{adj['answer']}\n"
                        adjustment_details += f"  选项：{adj.get('options', '{}')}\n\n"
                    
                    adjustment_details += "您是否要继续转换？"
                    
                    if not messagebox.askyesno("题目类型调整确认", adjustment_details):
                        messagebox.showinfo("取消", "转换已取消，未生成文件。")
                        return # 用户选择不继续，则停止转换

                messagebox.showinfo("转换成功", f"""CSV文件已成功转换为TXT文件！\n输出路径: {result.get("output_path")}\n成功转换: {result.get("converted_count")} 题\n失败转换: {result.get("failed_count")} 题""")
                # 如果用户确认继续，则在这里可以再次调用转换，或者调整后的逻辑已经自动完成
                # 由于我们是在convert方法内部进行调整的，这里只需确认并提示用户即可
            else:
                messagebox.showerror("转换失败", f"""TXT转换失败！\n错误信息: {result.get("message", "未知错误")}\n错误详情: {result.get("errors", "无")}""")
        except Exception as e:
            messagebox.showerror("错误", f"TXT转换过程中发生错误: {e}")

    def _on_convert_csv_to_docx_button_click(self):
        csv_file_path = self.conversion_input_path_var.get()
        output_dir = self.conversion_output_dir_var.get()

        if not csv_file_path or not output_dir:
            messagebox.showwarning("警告", "请选择CSV输入文件和输出目录！")
            return

        try:
            converter = QuizToDocxConverter() # 实例化DOCX转换器
            # 调用转换器的convert方法，现在它会返回调整过的题目信息
            result = converter.convert(csv_file_path, output_dir)

            if result.get("success"): # DOCX转换器返回的是"success"键
                adjusted_questions = result.get("adjusted_questions", [])
                if adjusted_questions: # 如果有题目被调整
                    adjustment_details = "以下题目类型已根据答案数量自动调整：\n\n"
                    for adj in adjusted_questions:
                        adjustment_details += f"题目：{adj['question'][:50]}...\n"
                        adjustment_details += f"  原始类型：{adj['original_type']} -> 调整后类型：{adj['adjusted_type']}\n"
                        adjustment_details += f"  答案：{adj['answer']}\n"
                        adjustment_details += f"  选项：{adj.get('options', '{}')}\n\n"
                    
                    adjustment_details += "您是否要继续转换？"
                    
                    if not messagebox.askyesno("题目类型调整确认", adjustment_details):
                        messagebox.showinfo("取消", "转换已取消，未生成文件。")
                        return # 用户选择不继续，则停止转换

                messagebox.showinfo("转换成功", f"""CSV文件已成功转换为DOCX文件！\n输出路径: {result.get("output_path")}\n成功转换: {result.get("successful_conversions")} 题\n失败转换: {result.get("failed_conversions")} 题""")
            else:
                messagebox.showerror("转换失败", f"""DOCX转换失败！\n错误信息: {result.get("message", "未知错误")}\n错误详情: {result.get("errors", "无")}""")
        except Exception as e:
            messagebox.showerror("错误", f"DOCX转换过程中发生错误: {e}")

    def _on_convert_csv_to_new_csv_button_click(self):
        csv_file_path = self.conversion_input_path_var.get()
        output_dir = self.conversion_output_dir_var.get()

        if not csv_file_path or not output_dir:
            messagebox.showwarning("警告", "请选择CSV输入文件和输出目录！")
            return

        try:
            # 这里的输出文件名可以根据您的需求定制，例如从UI获取或者固定
            output_file_name = "客观题导入模板_utf8.csv" # 假设这是目标文件名
            output_file_path = os.path.join(output_dir, output_file_name)

            converter = QuizFormatConverter(csv_file_path, output_file_path)
            result = converter.convert()

            if result.get("success"):
                messagebox.showinfo("转换成功", f"""CSV文件已成功转换为新的CSV文件！\n输出路径: {output_file_path}\n成功转换: {result.get("converted_count", 0)} 题\n过滤题目: {result.get("skipped_count", 0)} 题""")
            else:
                messagebox.showerror("转换失败", f"""CSV转换失败！\n错误信息: {result.get("message", "未知错误")}\n错误详情: {result.get("errors", "无")}""")
        except Exception as e:
            messagebox.showerror("错误", f"CSV转换过程中发生错误: {e}")

    def create_file_selection_frame(self):
        file_frame = ttk.LabelFrame(self.left_frame, text="文件选择", padding="5")
        file_frame.pack(fill=tk.X, pady=5)

        # 文件选择按钮
        ttk.Button(file_frame, text="选择文件", command=self.select_file).grid(row=0, column=0, padx=5)
        ttk.Button(file_frame, text="选择文件夹", command=self.select_folder).grid(row=0, column=1, padx=5)

        # 递归选项
        self.recursive_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(file_frame, text="递归读取子文件夹", variable=self.recursive_var).grid(row=0, column=2, padx=5)

        # 显示选择的路径
        self.file_path_var = tk.StringVar()
        ttk.Label(file_frame, textvariable=self.file_path_var).grid(row=1, column=0, columnspan=3, sticky=tk.W)

        # 文件类型选择
        file_types_frame = ttk.LabelFrame(file_frame, text="选择处理的文件类型", padding="3")
        file_types_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # 创建文件类型复选框变量
        self.enable_docx_var = tk.BooleanVar(value=True)
        self.enable_md_var = tk.BooleanVar(value=True)
        self.enable_txt_var = tk.BooleanVar(value=True)

        # 创建复选框
        ttk.Checkbutton(file_types_frame, text="DOCX", variable=self.enable_docx_var).grid(row=0, column=0, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="MD", variable=self.enable_md_var).grid(row=0, column=1, padx=5, sticky=tk.W)
        ttk.Checkbutton(file_types_frame, text="TXT", variable=self.enable_txt_var).grid(row=1, column=0, padx=5, sticky=tk.W)

        # 全选/全不选按钮
        button_frame = ttk.Frame(file_types_frame)
        button_frame.grid(row=1, column=2, padx=5, sticky=tk.W)
        ttk.Button(button_frame, text="全选", command=self.select_all_file_types, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="全不选", command=self.deselect_all_file_types, width=8).pack(side=tk.LEFT, padx=2)

        # 目录结构预览按钮
        ttk.Button(file_frame, text="预览目录结构", command=self.preview_directory).grid(row=3, column=0, columnspan=3, pady=5)

    def create_output_config_frame(self):
        """创建输出配置框架（在主界面中）"""
        config_frame = ttk.LabelFrame(self.left_frame, text="输出配置", padding="5")
        config_frame.pack(fill=tk.X, pady=5)

        # CSV文件位置
        ttk.Label(config_frame, text="CSV文件位置:").grid(row=0, column=0, sticky=tk.W)
        self.csv_path_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_path_var).grid(row=0, column=1, sticky=(tk.W, tk.E))
        ttk.Button(config_frame, text="浏览", command=self.select_csv_path).grid(row=0, column=2, padx=5)

        # CSV文件名
        ttk.Label(config_frame, text="CSV文件名:").grid(row=1, column=0, sticky=tk.W)
        self.csv_name_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.csv_name_var).grid(row=1, column=1, sticky=(tk.W, tk.E))

    def create_api_config_frame(self):
        """创建API配置框架（在设置标签页中）"""
        api_frame = ttk.LabelFrame(self.settings_frame, text="API配置", padding="5")
        api_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # API基础URL
        ttk.Label(api_frame, text="API基础URL:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.api_base_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_base_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # API密钥
        ttk.Label(api_frame, text="API密钥:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.api_key_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.api_key_var, show="*", width=40).grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 模型名称
        ttk.Label(api_frame, text="模型名称:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.model_name_var = tk.StringVar()
        ttk.Entry(api_frame, textvariable=self.model_name_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        # 温度设置
        ttk.Label(api_frame, text="温度:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.temperature_var = tk.DoubleVar(value=0.7)
        temp_frame = ttk.Frame(api_frame)
        temp_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Scale(temp_frame, from_=0.0, to=1.0, variable=self.temperature_var,
                 orient=tk.HORIZONTAL, length=200).pack(side=tk.LEFT)
        ttk.Label(temp_frame, textvariable=self.temperature_var).pack(side=tk.LEFT, padx=5)

        # 最大token数
        ttk.Label(api_frame, text="最大Token数:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.max_tokens_var = tk.IntVar(value=2000)
        ttk.Entry(api_frame, textvariable=self.max_tokens_var, width=40).grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)

        api_frame.columnconfigure(1, weight=1)

    def create_processing_config_frame(self):
        """创建文本处理配置框架（在设置标签页中）"""
        proc_frame = ttk.LabelFrame(self.settings_frame, text="文本处理配置", padding="5")
        proc_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 文本块大小
        ttk.Label(proc_frame, text="文本块大小 (tokens):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.max_chunk_size_var = tk.IntVar(value=2000)
        chunk_size_frame = ttk.Frame(proc_frame)
        chunk_size_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        chunk_scale = ttk.Scale(chunk_size_frame, from_=500, to=8000, variable=self.max_chunk_size_var,
                               orient=tk.HORIZONTAL, length=200)
        chunk_scale.pack(side=tk.LEFT)
        chunk_scale.configure(command=lambda v: self.max_chunk_size_var.set(int(float(v))))
        ttk.Label(chunk_size_frame, textvariable=self.max_chunk_size_var).pack(side=tk.LEFT, padx=5)
        # 添加文本块大小说明
        ttk.Label(proc_frame, text="每个文本块的最大token数，影响处理速度和质量。", font=("Arial", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W, padx=5)

        # 题目基础字数设置
        ttk.Label(proc_frame, text="题目基础字数:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.question_base_chars_var = tk.IntVar(value=2000)
        base_chars_frame = ttk.Frame(proc_frame)
        base_chars_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        base_chars_scale = ttk.Scale(base_chars_frame, from_=500, to=5000, variable=self.question_base_chars_var,
                                    orient=tk.HORIZONTAL, length=200)
        base_chars_scale.pack(side=tk.LEFT)
        base_chars_scale.configure(command=lambda v: self.question_base_chars_var.set(int(float(v))))
        ttk.Label(base_chars_frame, textvariable=self.question_base_chars_var).pack(side=tk.LEFT, padx=5)
        # 添加题目基础字数说明
        ttk.Label(proc_frame, text="生成一组题目的基础字数。", font=("Arial", 8), foreground="gray").grid(row=1, column=2, sticky=tk.W, padx=5)

        # 文档分割选项
        ttk.Label(proc_frame, text="文档分割:").grid(row=2, column=0, sticky=tk.W, pady=5)
        splitting_options_frame = ttk.Frame(proc_frame) # 新建一个frame来容纳复选框和说明
        splitting_options_frame.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5) # columnspan=2 以便容纳更多内容

        self.disable_splitting_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(splitting_options_frame, text="不进行文档分割",
                       variable=self.disable_splitting_var).pack(side=tk.LEFT)
        ttk.Label(splitting_options_frame, text="勾选后将对整个文档生成题目，忽略分块设置。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 新分块逻辑选项
        ttk.Label(proc_frame, text="分块逻辑:").grid(row=3, column=0, sticky=tk.W, pady=5)
        new_logic_options_frame = ttk.Frame(proc_frame) # 新建一个frame来容纳复选框和说明
        new_logic_options_frame.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E), padx=5, pady=5) # columnspan=2 以便容纳更多内容

        self.use_new_splitting_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(new_logic_options_frame, text="使用新分块逻辑",
                       variable=self.use_new_splitting_var).pack(side=tk.LEFT)
        ttk.Label(new_logic_options_frame, text="采用更智能的分段方式，通常生成无重叠的文本块，并根据字数进行调整。", font=("Arial", 8), foreground="gray").pack(side=tk.LEFT, padx=5)

        # 预设配置按钮
        preset_frame = ttk.Frame(proc_frame)
        preset_frame.grid(row=4, column=0, columnspan=3, pady=10)

        ttk.Button(preset_frame, text="快速配置", command=self.apply_fast_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="平衡配置", command=self.apply_balanced_preset).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="精细配置", command=self.apply_detailed_preset).pack(side=tk.LEFT, padx=5)

        proc_frame.columnconfigure(1, weight=1)

    def create_question_types_config_frame(self):
        """创建题型数量配置框架（在设置标签页中）"""
        types_frame = ttk.LabelFrame(self.settings_frame, text="题型数量配置", padding="5")
        types_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 题型配置说明 (移动到顶部右侧)
        info_text = "说明：设置每个文本块生成各种题型的数量，总题目数量为各题型数量之和。新出题逻辑将根据文档字数和基础字数自动调整题目数量。基础字数是指生成一道题所需的最少文本字数。基础题数是指在满足基础字数条件下，每个文本块或文档段落预期生成的题目数量。"
        ttk.Label(types_frame, text=info_text, font=("Arial", 8), foreground="gray", wraplength=300).grid(row=0, column=2, rowspan=7, sticky=tk.NW, padx=5, pady=5)

        # 单选题数量
        ttk.Label(types_frame, text="单选题数量:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.single_choice_var = tk.IntVar(value=4)
        single_frame = ttk.Frame(types_frame)
        single_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        single_scale = ttk.Scale(single_frame, from_=0, to=10, variable=self.single_choice_var,
                                orient=tk.HORIZONTAL, length=150)
        single_scale.pack(side=tk.LEFT)
        single_scale.configure(command=lambda v: self.single_choice_var.set(int(float(v))))
        ttk.Label(single_frame, textvariable=self.single_choice_var).pack(side=tk.LEFT, padx=5)

        # 多选题数量
        ttk.Label(types_frame, text="多选题数量:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.multiple_choice_var = tk.IntVar(value=2)
        multiple_frame = ttk.Frame(types_frame)
        multiple_frame.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        multiple_scale = ttk.Scale(multiple_frame, from_=0, to=10, variable=self.multiple_choice_var,
                                  orient=tk.HORIZONTAL, length=150)
        multiple_scale.pack(side=tk.LEFT)
        multiple_scale.configure(command=lambda v: self.multiple_choice_var.set(int(float(v))))
        ttk.Label(multiple_frame, textvariable=self.multiple_choice_var).pack(side=tk.LEFT, padx=5)

        # 填空题数量
        ttk.Label(types_frame, text="填空题数量:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.fill_blank_var = tk.IntVar(value=2)
        fill_frame = ttk.Frame(types_frame)
        fill_frame.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        fill_scale = ttk.Scale(fill_frame, from_=0, to=10, variable=self.fill_blank_var,
                              orient=tk.HORIZONTAL, length=150)
        fill_scale.pack(side=tk.LEFT)
        fill_scale.configure(command=lambda v: self.fill_blank_var.set(int(float(v))))
        ttk.Label(fill_frame, textvariable=self.fill_blank_var).pack(side=tk.LEFT, padx=5)

        # 简答题数量
        ttk.Label(types_frame, text="简答题数量:").grid(row=4, column=0, sticky=tk.W, pady=5)
        self.short_answer_var = tk.IntVar(value=1)
        short_frame = ttk.Frame(types_frame)
        short_frame.grid(row=4, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        short_scale = ttk.Scale(short_frame, from_=0, to=10, variable=self.short_answer_var,
                               orient=tk.HORIZONTAL, length=150)
        short_scale.pack(side=tk.LEFT)
        short_scale.configure(command=lambda v: self.short_answer_var.set(int(float(v))))
        ttk.Label(short_frame, textvariable=self.short_answer_var).pack(side=tk.LEFT, padx=5)

        # 判断题数量
        ttk.Label(types_frame, text="判断题数量:").grid(row=5, column=0, sticky=tk.W, pady=5)
        self.true_false_var = tk.IntVar(value=2)
        judge_frame = ttk.Frame(types_frame)
        judge_frame.grid(row=5, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        judge_scale = ttk.Scale(judge_frame, from_=0, to=10, variable=self.true_false_var,
                               orient=tk.HORIZONTAL, length=150)
        judge_scale.pack(side=tk.LEFT)
        judge_scale.configure(command=lambda v: self.true_false_var.set(int(float(v))))
        ttk.Label(judge_frame, textvariable=self.true_false_var).pack(side=tk.LEFT, padx=5)

        # 排序题数量
        ttk.Label(types_frame, text="排序题数量:").grid(row=6, column=0, sticky=tk.W, pady=5)
        self.sorting_var = tk.IntVar(value=0) # 默认为0，因为排序题比较复杂
        sorting_frame = ttk.Frame(types_frame)
        sorting_frame.grid(row=6, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        sorting_scale = ttk.Scale(sorting_frame, from_=0, to=10, variable=self.sorting_var,
                                 orient=tk.HORIZONTAL, length=150)
        sorting_scale.pack(side=tk.LEFT)
        sorting_scale.configure(command=lambda v: self.sorting_var.set(int(float(v))))
        ttk.Label(sorting_frame, textvariable=self.sorting_var).pack(side=tk.LEFT, padx=5)

        types_frame.columnconfigure(1, weight=1)

    def create_save_settings_button(self):
        """创建保存设置按钮（在设置标签页中）"""
        ttk.Button(self.settings_frame, text="保存设置", command=self.save_config_and_notify).grid(row=3, column=0, columnspan=2, pady=10)

    def create_execute_button(self):
        button_frame = ttk.Frame(self.left_frame)
        button_frame.pack(fill=tk.X, pady=10)

        self.execute_btn = ttk.Button(button_frame, text="开始处理", command=self.execute)
        self.execute_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_btn = ttk.Button(button_frame, text="停止处理", command=self.stop_processing, state='disabled')
        self.stop_btn.pack(side=tk.LEFT)

        # 将按钮添加到gui_vars中供ProcessingManager使用
        self.gui_vars['execute_btn'] = self.execute_btn
        self.gui_vars['stop_btn'] = self.stop_btn

    def create_status_frame(self):
        status_frame = ttk.LabelFrame(self.left_frame, text="状态", padding="5")
        status_frame.pack(fill=tk.X, pady=5)

        self.status_var = tk.StringVar()
        ttk.Label(status_frame, textvariable=self.status_var).pack(anchor=tk.W)

    def clear_question_display(self):
        """清空题目显示"""
        self.question_display_panel.clear_question_display()

    def save_displayed_questions(self):
        """保存显示的题目到文件"""
        content = self.question_display.get(1.0, tk.END)
        if not content.strip():
            messagebox.showwarning("警告", "没有题目可保存")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.* подойдёт")]
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("成功", f"题目已保存到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def add_question_to_display(self, question_data):
        """添加题目到显示区域"""
        self.question_display_panel.add_question_to_display(question_data)

    def check_question_queue(self):
        """检查题目队列并更新显示"""
        try:
            while True:
                message = self.question_queue.get_nowait()

                # 防御性检查：确保message不为None且是字典类型
                if message is None or not isinstance(message, dict):
                    logger.warning(f"收到无效的队列消息: {message}, 类型: {type(message)}")
                    continue

                message_type = message.get('type')
                logger.debug(f"处理队列消息: type={message_type}, message={message}")

                if message_type == 'question':
                    # 新题目
                    self.add_question_to_display(message.get('data'))
                    self.has_generated_questions = True # 有题目生成，设置标志
                elif message_type == 'progress':
                    # 进度更新
                    progress_data = message.get('data')
                    current = progress_data.get('current', 0)
                    total = progress_data.get('total', 0)
                    filename = progress_data.get('filename', '')

                    self.progress_var.set(f"{current}/{total}")
                    if total > 0:
                        self.progress_bar['maximum'] = total
                        self.progress_bar['value'] = current

                    self.status_var.set(f"正在处理: {filename} ({current}/{total})")
                    # 在题目显示区域显示进度，例如：
                    self.question_display.insert(tk.END, f"[进度] 正在处理: {filename} ({current}/{total})\n")
                    self.question_display.see(tk.END)
                elif message_type == 'status':
                    # 状态更新
                    self.status_var.set(message.get('data', ''))
                elif message_type == 'complete':
                    # 处理完成
                    self.processing_complete()
                elif message_type == 'failure':
                    # 生成失败信息，需要显示在GUI上
                    failure_data = message.get('data', {})
                    filename = failure_data.get('filename', '未知文件')
                    chunk_index = failure_data.get('chunk_index', 0)
                    reason = failure_data.get('reason', '未知原因')
                    chunk_content = failure_data.get('chunk_content', '无内容') # 提取文本内容
                    char_count = len(chunk_content) if chunk_content != '无内容' else 0

                    # 在GUI显示详细信息并进入错误记录流程
                    failure_text = f"❌ 生成失败 - 文件: {filename} (块 {chunk_index+1})\n"
                    failure_text += f"   原因: {reason}\n"
                    failure_text += f"   失败文本段落:\n"
                    failure_text += f"   ----------------------------------------\n"
                    failure_text += f"   {chunk_content}\n"
                    failure_text += f"   ----------------------------------------\n"
                    failure_text += f"   提示：此失败已记录在 'err_docs' 文件夹中。\n"
                    failure_text += "="*50 + "\n"

                    # 在题目显示区域显示失败信息
                    self.question_display.insert(tk.END, failure_text)
                    self.question_display.see(tk.END)
                    logger.error(f"处理文本块失败: {filename} - 分块: {chunk_index+1}, 字数: {char_count}, 原因: {reason}")

                elif message_type == 'log_only_failure':
                    # 仅记录到专门的错误日志文件，不显示在GUI上，也不输出到控制台
                    failure_data = message.get('data', {})
                    filename = failure_data.get('filename', '未知文件')
                    chunk_index = failure_data.get('chunk_index', 0)
                    reason = failure_data.get('reason', '未知原因')
                    chunk_content = failure_data.get('chunk_content', '无内容')
                    char_count = len(chunk_content) if chunk_content != '无内容' else 0

                    # 使用ErrorManager记录到专门的错误日志文件
                    try:
                        from error_handler.error_manager import ErrorManager
                        from config import Config
                        config = Config()
                        error_manager = ErrorManager(config)
                        error_manager._log_chunk_failure(filename, chunk_index, char_count, reason)
                    except Exception as e:
                        # 如果ErrorManager失败，至少记录到普通日志，但不输出WARNING级别
                        logger.debug(f"文本块处理失败记录: 文件={filename}, 分块={chunk_index+1}, 字数={char_count}, 原因={reason}")
                elif message_type == 'merge_info':
                    # 分块合并信息
                    merge_data = message.get('data', {})
                    filename = merge_data.get('filename', '未知文件')
                    original_index = merge_data.get('original_index', 0)
                    merged_to_index = merge_data.get('merged_to_index', 0)
                    char_count = merge_data.get('char_count', 0)

                    merge_text = f"🔗 分块合并: {filename}\n"
                    merge_text += f"   索引 {original_index} 合并到 {merged_to_index}\n"
                    merge_text += f"   合并后字符数: {char_count}\n"
                    merge_text += "="*50 + "\n"

                    # 在题目显示区域显示合并信息
                    self.question_display.insert(tk.END, merge_text)
                    self.question_display.see(tk.END)
                elif message_type == 'incremental_save':
                    # 增量保存信息
                    save_data = message.get('data', {})
                    questions_saved = save_data.get('questions_saved', 0)
                    total_saved = save_data.get('total_saved', 0)
                    file_path = save_data.get('file_path', '')
                    backup_created = save_data.get('backup_created', False)

                    save_text = f"💾 增量保存: {questions_saved} 道题目 (总计: {total_saved})\n"
                    if backup_created:
                        save_text += "📦 自动备份已创建\n"
                    save_text += f"📁 文件: {os.path.basename(file_path) if file_path else '未知'}\n"
                    save_text += "="*50 + "\n"

                    # 在题目显示区域显示保存信息
                    self.question_display.insert(tk.END, save_text)
                    self.question_display.see(tk.END)
                elif message_type == 'final_save':
                    # 最终保存信息
                    save_data = message.get('data', {})
                    questions_saved = save_data.get('questions_saved', 0)
                    total_saved = save_data.get('total_saved', 0)
                    file_path = save_data.get('file_path', '')

                    if questions_saved > 0:
                        save_text = f"✅ 最终保存: {questions_saved} 道题目 (总计: {total_saved})\n"
                        save_text += f"📁 文件: {os.path.basename(file_path) if file_path else '未知'}\n"
                        save_text += "="*50 + "\n"

                        # 在题目显示区域显示保存信息
                        self.question_display.insert(tk.END, save_text)
                        self.question_display.see(tk.END)
                elif message_type == 'error':
                    # 错误信息
                    error_msg = message.get('data', '未知错误')
                    self.status_var.set(f"错误: {error_msg}")
                    messagebox.showerror("处理错误", error_msg)
                    self.processing_complete()

        except queue.Empty:
            pass
        except Exception as e:
            import traceback
            logger.error(f"check_question_queue 发生异常: {str(e)}")
            logger.error(f"异常堆栈: {traceback.format_exc()}")

        # 继续检查队列
        self.root.after(100, self.check_question_queue)

    def processing_complete(self):
        """处理完成后的清理工作"""
        self.is_processing = False
        self.execute_btn.config(state=tk.NORMAL, text="开始处理")
        self.stop_btn.config(state=tk.DISABLED)
        self.status_var.set("处理完成")

    def stop_processing(self):
        """停止处理"""
        self.processing_manager.stop_processing()

    def preview_directory(self):
        """预览目录结构"""
        directory = self.file_path_var.get()
        if not directory:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        if not os.path.isdir(directory):
            messagebox.showwarning("警告", "选择的路径不是文件夹")
            return

        try:
            recursive = self.recursive_var.get()
            # 获取当前启用的文件类型
            enabled_formats = []
            if self.enable_docx_var.get():
                enabled_formats.append('.docx')
            if self.enable_md_var.get():
                enabled_formats.append('.md')
            if self.enable_txt_var.get():
                enabled_formats.append('.txt')

            structure_info = scan_directory_structure(directory, recursive, enabled_formats)

            if 'error' in structure_info:
                messagebox.showerror("错误", structure_info['error'])
                return

            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("目录结构预览")
            preview_window.geometry("600x400")

            # 创建文本框显示结构信息
            text_frame = ttk.Frame(preview_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 构建显示内容
            content = f"目录结构预览 (递归: {recursive})\n"
            content += "=" * 50 + "\n\n"
            content += f"总文件数: {structure_info['total_files']}\n"
            content += f"支持的文档数: {structure_info['supported_files']}\n"
            content += f"文件类型分布: {structure_info['file_types']}\n\n"

            content += "各目录文件分布:\n"
            content += "-" * 30 + "\n"

            for directory_path, files in structure_info['files_by_directory'].items():
                if files:  # 只显示有支持文件的目录
                    display_path = directory_path if directory_path else "根目录"
                    content += f"\n📁 {display_path} ({len(files)} 个文件):\n"
                    for file in files:
                        content += f"  📄 {file}\n"

            text_widget.insert(tk.END, content)
            text_widget.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"预览目录结构时发生错误：{str(e)}")

    def select_all_file_types(self):
        """全选文件类型"""
        self.enable_docx_var.set(True)
        self.enable_md_var.set(True)
        self.enable_txt_var.set(True)

    def deselect_all_file_types(self):
        """全不选文件类型"""
        self.enable_docx_var.set(False)
        self.enable_md_var.set(False)
        self.enable_txt_var.set(False)

    def apply_fast_preset(self):
        """应用快速配置预设"""
        self.max_chunk_size_var.set(1500)
        self.question_base_chars_var.set(1500)
        # 快速配置：较少题目
        self.single_choice_var.set(1)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(0)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用快速配置：\n• 文本块大小: 1500 字符\n• 题目基础字数: 1500\n• 题型: 单选1+多选1+填空1+判断1")

    def apply_balanced_preset(self):
        """应用平衡配置预设"""
        self.max_chunk_size_var.set(2000)
        self.question_base_chars_var.set(2000)
        # 平衡配置：中等题目数量
        self.single_choice_var.set(2)
        self.multiple_choice_var.set(1)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(1)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用平衡配置：\n• 文本块大小: 2000 字符\n• 题目基础字数: 2000\n• 题型: 单选2+多选1+填空1+简答1+判断1")

    def apply_detailed_preset(self):
        """应用精细配置预设"""
        self.max_chunk_size_var.set(5000)
        self.question_base_chars_var.set(1300)
        # 精细配置：较多题目
        self.single_choice_var.set(4)
        self.multiple_choice_var.set(2)
        self.fill_blank_var.set(1)
        self.short_answer_var.set(1)
        self.true_false_var.set(2)
        self.sorting_var.set(0)
        messagebox.showinfo("配置应用", "已应用精细配置：\n• 文本块大小: 5000 字符\n• 题目基础字数: 1300\n• 题型: 单选4+多选2+填空1+简答1+判断2+排序0")

    def test_connection(self):
        try:
            # 先保存当前配置
            self.save_config()
            # 加载配置
            config = Config.load_config()
            # 创建处理器实例
            processor = DocumentToQuizProcessor(config)
            # 测试连接
            if processor.test_api_connection():
                messagebox.showinfo("成功", "API连接测试成功！")
            else:
                messagebox.showerror("错误", "API连接测试失败！")
        except Exception as e:
            messagebox.showerror("错误", f"测试连接时发生错误：{str(e)}")

    def save_config(self):
        """保存配置"""
        self.config_manager.save_config_and_notify(self.gui_vars, self.status_var)

    def save_config_and_notify(self):
        """保存设置并显示通知"""
        self.save_config()
        messagebox.showinfo("成功", "设置已保存")

    def execute(self):
        """执行文档处理"""
        # 先保存当前配置
        self.save_config()

        # 重新加载配置以确保使用最新的配置
        updated_config = Config.load_config()

        # 更新处理管理器的配置
        self.processing_manager.update_config(updated_config)

        # 使用ProcessingManager执行处理
        self.processing_manager.execute(self.clear_question_display)

    def processing_complete(self):
        """处理完成后的清理工作"""
        # 这个方法现在由ProcessingManager处理
        pass

    def check_question_queue(self):
        """检查题目队列并更新显示"""
        # 这个方法现在由ProcessingManager处理
        self.processing_manager.check_question_queue(self.add_question_to_display)

    def save_displayed_questions(self):
        """保存显示的题目"""
        self.question_display_panel.save_displayed_questions()

if __name__ == "__main__":
    root = tk.Tk()
    app = MainApplication(root)
    root.mainloop()