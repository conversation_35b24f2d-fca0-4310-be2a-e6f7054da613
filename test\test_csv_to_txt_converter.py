import unittest
import os
import shutil
import logging
import textwrap
from csv_to_tdocx.csv_to_txt_converter import QuizToTxtConverter

# 设置测试环境的日志级别，避免测试输出过多不必要的日志
logging.basicConfig(level=logging.CRITICAL)

class TestQuizToTxtConverter(unittest.TestCase):

    def setUp(self):
        """
        在每个测试方法执行前创建临时文件和目录。
        """
        self.test_dir = "./test_output_temp"
        os.makedirs(self.test_dir, exist_ok=True)
        self.csv_input_path = os.path.join(self.test_dir, "test_input.csv")
        # self.txt_output_path = os.path.join(self.test_dir, "test_output.txt") # Old fixed name
        # Dynamically set output path based on input CSV name
        self.txt_output_path = os.path.join(self.test_dir, os.path.basename(self.csv_input_path).replace('.csv', '.txt'))
        self.template_path = "新模板.ini"
        self.converter = QuizToTxtConverter(self.template_path)

    def tearDown(self):
        """
        在每个测试方法执行后清理临时文件和目录。
        """
        if os.path.exists(self.test_dir):
            shutil.rmtree(self.test_dir)

    def _create_csv_file(self, content):
        with open(self.csv_input_path, 'w', newline='', encoding='utf-8') as f:
            f.write(textwrap.dedent(content))

    def _read_txt_file(self, path):
        with open(path, 'r', encoding='utf-8') as f:
            return f.read()

    # TODO: 添加_load_config的测试

    # Test _read_csv method
    def test_read_csv_basic(self):
        csv_content = """题型,题目,选项A,选项B,选项C,答案,解析,题目难度,知识点,关键词
单选题,问题1,选项1A,选项1B,选项1C,,A,解析1,简单,知识点1|知识点2,
多选题,问题2,选项2A,选项2B,选项2C,选项2D,AB,解析2,一般,知识点3,
判断题,问题3,,,对,解析3,较难,知识点4,
填空题,问题4【】,空1答案1/空1答案2|空2答案1/空2答案2,,,,,,
简答题,问题5,,,,,,,,,关键词1/{/}/关键词2
"""
        self._create_csv_file(csv_content)
        data = self.converter._read_csv(self.csv_input_path)
        self.assertEqual(len(data), 5)
        self.assertEqual(data[0]['题目'], '问题1')
        self.assertEqual(data[4]['题型'], '简答题')

    def test_read_csv_file_not_found(self):
        with self.assertRaises(FileNotFoundError):
            self.converter._read_csv("non_existent.csv")

    def test_read_csv_empty_file(self):
        self._create_csv_file("题型,题目,选项A,答案,解析") # Only header
        data = self.converter._read_csv(self.csv_input_path)
        self.assertEqual(len(data), 0)

    # Test _parse_question_data method
    def test_parse_question_data_single_choice(self):
        row = {'题型': '单选题', '题目': '问题1', '选项A': '选项1A', '选项B': '选项1B', '答案': 'A', '解析': '解析1', '题目难度': '简单', '知识点': '知识点1|知识点2'}
        parsed = self.converter._parse_question_data(row)
        self.assertEqual(parsed['type'], '单选题')
        self.assertEqual(parsed['question'], '问题1')
        self.assertEqual(parsed['answer'], 'A')
        self.assertEqual(parsed['options']['A'], '选项1A')
        self.assertIn('知识点1', parsed['knowledge_points'])

    def test_parse_question_data_fill_in_blank(self):
        row = {'题型': '填空题', '题目': '问题4【】', '答案': '空1答案1/空1答案2|空2答案1/空2答案2'}
        parsed = self.converter._parse_question_data(row)
        self.assertEqual(parsed['type'], '填空题')
        self.assertEqual(parsed['answer'], [['空1答案1', '空1答案2'], ['空2答案1', '空2答案2']])

    def test_parse_question_data_short_answer_with_escaped_slash(self):
        row = {'题型': '简答题', '题目': '问题5', '关键词': '关键词1/{/}/关键词2'}
        parsed = self.converter._parse_question_data(row)
        self.assertEqual(parsed['type'], '简答题')
        self.assertEqual(parsed['keywords'], ['关键词1/{/}/关键词2'])

    # Test _format_single_choice
    def test_format_single_choice(self):
        data = {'type': '单选题', 'question': 'Test Single Choice', 'options': {'A': 'Option A', 'B': 'Option B'}, 'answer': 'A', 'explanation': 'Test explanation', 'difficulty': '简单', 'knowledge_points': ['知识点A', '知识点B']}
        expected_output = """
【单选题】Test Single Choice
A、Option A
B、Option B
正确答案： A
题目难度：简单
答案解析：Test explanation
知识点：知识点A、知识点B
"""
        self.assertEqual(self.converter._format_single_choice(data), expected_output.lstrip("\n")) # remove leading newline

    # Test _format_multiple_choice
    def test_format_multiple_choice(self):
        data = {'type': '多选题', 'question': 'Test Multiple Choice', 'options': {'A': 'Option A', 'B': 'Option B', 'C': 'Option C'}, 'answer': 'AB', 'explanation': 'Test explanation', 'difficulty': '一般', 'knowledge_points': ['知识点C']}
        expected_output = """
【多选题】Test Multiple Choice
A、Option A
B、Option B
C、Option C
正确答案：AB
题目难度：一般
答案解析：Test explanation
知识点：知识点C
"""
        self.assertEqual(self.converter._format_multiple_choice(data), expected_output.lstrip("\n")) # remove leading newline

    # Test _format_true_false
    def test_format_true_false(self):
        data = {'type': '判断题', 'question': 'Test True False', 'answer': '对', 'explanation': 'Test explanation', 'difficulty': '较难', 'knowledge_points': []}
        expected_output = """
【判断题】Test True False
正确答案：对
题目难度：较难
答案解析：Test explanation
知识点：暂无知识点
"""
        self.assertEqual(self.converter._format_true_false(data), expected_output.lstrip("\n")) # remove leading newline

    # Test _format_fill_in_blank
    def test_format_fill_in_blank(self):
        data = {'type': '填空题', 'question': 'Fill in the 【】 blank.', 'answer': [['ans1a', 'ans1b']], 'explanation': 'Exp', 'difficulty': '简单', 'knowledge_points': []}
        expected_output = """
【填空题】Fill in the 【ans1a/ans1b】 blank.
题目难度：简单
作答上传图片：否
答案解析：Exp
知识点：暂无知识点
"""
        self.assertEqual(self.converter._format_fill_in_blank(data), expected_output.lstrip("\n")) # remove leading newline

    def test_format_fill_in_blank_with_escaped_slash_in_answer(self):
        data = {'type': '填空题', 'question': 'Fill in the 【】 blank.', 'answer': [['ans1a', 'ans/1b']], 'explanation': 'Exp', 'difficulty': '简单', 'knowledge_points': []}
        expected_output = """
【填空题】Fill in the 【ans1a/ans{/}1b】 blank.
题目难度：简单
作答上传图片：否
答案解析：Exp
知识点：暂无知识点
"""
        self.assertEqual(self.converter._format_fill_in_blank(data), expected_output.lstrip("\n")) # remove leading newline

    def test_format_fill_in_blank_multiple_blanks_multiple_options(self):
        data = {'type': '填空题', 'question': 'Q with 【】 and 【】 blanks.', 'answer': [['ans1_A', 'ans1_B'], ['ans2_C', 'ans2/D']], 'explanation': 'Exp', 'difficulty': '简单', 'knowledge_points': ['Fill','Test']}
        expected_output = """
【填空题】Q with 【ans1_A/ans1_B】 and 【ans2_C/ans2{/}D】 blanks.
题目难度：简单
作答上传图片：否
答案解析：Exp
知识点：Fill、Test
"""
        self.assertEqual(self.converter._format_fill_in_blank(data), expected_output.lstrip("\n")) # remove leading newline


    # Test _format_short_answer
    def test_format_short_answer(self):
        data = {'type': '简答题', 'question': 'Test Short Answer', 'keywords': ['key1', 'key2'], 'explanation': 'Exp', 'difficulty': '困难', 'knowledge_points': ['SA']}
        expected_output = """
【简答题】Test Short Answer
关键字1：key1
关键字2：key2
题目难度：困难
作答上传图片：否
答案解析：Exp
知识点：SA
"""
        self.assertEqual(self.converter._format_short_answer(data), expected_output.lstrip("\n")) # remove leading newline

    def test_format_short_answer_with_escaped_slash_in_keywords(self):
        data = {'type': '简答题', 'question': 'Test Short Answer', 'keywords': ['key1', 'key/2'], 'explanation': 'Exp', 'difficulty': '困难', 'knowledge_points': ['SA']}
        # Note: _parse_question_data handles the unescaping, so keywords should be 'key1', 'key/2' here
        expected_output = """
【简答题】Test Short Answer
关键字1：key1
关键字2：key/2
题目难度：困难
作答上传图片：否
答案解析：Exp
知识点：SA
"""
        self.assertEqual(self.converter._format_short_answer(data), expected_output.lstrip("\n")) # remove leading newline

    # Test _write_txt method
    def test_write_txt_basic(self):
        content = "Hello World"
        self.converter._write_txt(content, self.txt_output_path)
        self.assertTrue(os.path.exists(self.txt_output_path))
        with open(self.txt_output_path, 'r', encoding='utf-8') as f:
            self.assertEqual(f.read(), content)

    def test_write_txt_creates_directory(self):
        nested_output_path = os.path.join(self.test_dir, "subdir", "output.txt")
        content = "Nested content"
        self.converter._write_txt(content, nested_output_path)
        self.assertTrue(os.path.exists(nested_output_path))
        self.assertTrue(os.path.isdir(os.path.join(self.test_dir, "subdir")))

    # Test convert method (End-to-End)
    def test_convert_success(self):
        csv_content = """题型,题目,选项A,选项B,答案,解析,题目难度,知识点
单选题,这是单选问题,选项A,选项B,A,解析,简单,计算机基础
判断题,这是判断问题,,,对,这是判断解析,一般,逻辑推理
"""
        self._create_csv_file(csv_content)
        result = self.converter.convert(self.csv_input_path, self.test_dir)

        self.assertEqual(result['status'], "success")
        self.assertEqual(result['converted_count'], 2)
        self.assertEqual(result['failed_count'], 0)
        self.assertTrue(os.path.exists(self.txt_output_path))

        output_content = self._read_txt_file(self.txt_output_path)
        self.assertIn("【单选题】这是单选问题", output_content)
        self.assertIn("【判断题】这是判断问题", output_content)

    def test_convert_input_file_not_found(self):
        result = self.converter.convert("non_existent.csv", self.test_dir)
        self.assertEqual(result['status'], "error")
        self.assertIn("文件不存在", result['message'])

    def test_convert_empty_csv(self):
        self._create_csv_file("题型,题目,选项A,答案,解析") # Only header
        result = self.converter.convert(self.csv_input_path, self.test_dir)
        self.assertEqual(result['status'], "success")
        self.assertEqual(result['converted_count'], 0)
        self.assertIn("文件为空或未读取到任何题目数据", result['message'])

    def test_convert_with_some_errors_in_rows(self):
        csv_content = """题型,题目,选项A,选项B,答案,解析,题目难度,知识点
单选题,这是有效问题,选项A,选项B,A,解析,简单,知识点1
未知题型,这是无效问题,,,,,,,
单选题,这是另一个有效问题,选项X,选项Y,X,解析,一般,知识点2
"""
        self._create_csv_file(csv_content)
        result = self.converter.convert(self.csv_input_path, self.test_dir)

        self.assertEqual(result['status'], "success") # Still considered success if some convert
        self.assertEqual(result['converted_count'], 2)
        self.assertEqual(result['failed_count'], 1)
        self.assertEqual(len(result['errors']), 1)
        self.assertIn("未知题型", result['errors'][0]['reason'])
        self.assertTrue(os.path.exists(self.txt_output_path))

        output_content = self._read_txt_file(self.txt_output_path)
        self.assertIn("【单选题】这是有效问题", output_content)
        self.assertIn("【单选题】这是另一个有效问题", output_content)
        self.assertNotIn("未知题型", output_content)


if __name__ == '__main__':
    unittest.main() 