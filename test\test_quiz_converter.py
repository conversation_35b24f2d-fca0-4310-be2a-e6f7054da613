import unittest
import pandas as pd
from unittest.mock import patch, mock_open
import os

# Adjust the import path as necessary based on your project structure
# Assuming quiz_converter is a package and quiz_formatter.py is a module inside it
from quiz_converter.quiz_formatter import QuizFormatConverter

class TestQuizFormatConverter(unittest.TestCase):

    def setUp(self):
        self.source_file = 'dummy_source.csv'
        self.target_file = 'dummy_target.csv'
        self.converter = QuizFormatConverter(self.source_file, self.target_file)

    @patch('pandas.read_csv')
    def test_read_csv_utf8_sig_success(self, mock_read_csv):
        mock_read_csv.return_value = pd.DataFrame({'col1': [1, 2]})
        df = self.converter._read_csv()
        mock_read_csv.assert_called_once_with(self.source_file, encoding='utf-8-sig')
        self.assertFalse(df.empty)

    @patch('pandas.read_csv')
    def test_read_csv_utf8_fallback(self, mock_read_csv):
        mock_read_csv.side_effect = [UnicodeDecodeError("utf-8-sig", b'', 0, 1, ""), pd.DataFrame({'col1': [1, 2]})]
        df = self.converter._read_csv()
        self.assertEqual(mock_read_csv.call_args_list[0].kwargs['encoding'], 'utf-8-sig')
        self.assertEqual(mock_read_csv.call_args_list[1].kwargs['encoding'], 'utf-8')
        self.assertFalse(df.empty)

    def test_convert_difficulty(self):
        self.assertEqual(self.converter._convert_difficulty('低'), '简单')
        self.assertEqual(self.converter._convert_difficulty('中'), '中等')
        self.assertEqual(self.converter._convert_difficulty('高'), '困难')
        self.assertEqual(self.converter._convert_difficulty('未知'), '')

    def test_handle_options_single_choice(self):
        options_str = 'A. Option A|B. Option B'
        quiz_type = '单选'
        result = self.converter._handle_options(options_str, quiz_type)
        self.assertEqual(result['*A'], 'A. Option A')
        self.assertEqual(result['*B'], 'B. Option B')
        self.assertEqual(result['C'], '')

    def test_handle_options_multiple_choice(self):
        options_str = 'A. Opt1|B. Opt2|C. Opt3|D. Opt4'
        quiz_type = '多选'
        result = self.converter._handle_options(options_str, quiz_type)
        self.assertEqual(result['*A'], 'A. Opt1')
        self.assertEqual(result['*B'], 'B. Opt2')
        self.assertEqual(result['C'], 'C. Opt3')
        self.assertEqual(result['D'], 'D. Opt4')

    def test_handle_options_judgment(self):
        options_str = ''
        quiz_type = '判断'
        result = self.converter._handle_options(options_str, quiz_type)
        self.assertEqual(result['*A'], '正确')
        self.assertEqual(result['*B'], '错误')
        self.assertEqual(result['C'], '')

    def test_process_row_valid(self):
        row = {
            '试题题干(必填)': 'Question Text',
            '试题类型(必填)': '单选',
            '选项（用\'|\'隔开）': 'A. Opt1|B. Opt2',
            '答案（填空题用\'|\'隔开）': 'A',
            '难易度': '低',
            '试题解析': 'Explanation'
        }
        processed_row = self.converter._process_row(row)
        self.assertIsNotNone(processed_row)
        self.assertEqual(processed_row['*题目'], 'Question Text')
        self.assertEqual(processed_row['*题型'], '单选')
        self.assertEqual(processed_row['*答案'], 'A')
        self.assertEqual(processed_row['*难度'], '简单')
        self.assertEqual(processed_row['*A'], 'A. Opt1')

    def test_process_row_skipped_type(self):
        row = {
            '试题题干(必填)': 'Question Text',
            '试题类型(必填)': '填空',  # This type should be skipped
            '选项（用\'|\'隔开）': '',
            '答案（填空题用\'|\'隔开）': '',
            '难易度': '低',
            '试题解析': ''
        }
        processed_row = self.converter._process_row(row)
        self.assertIsNone(processed_row)

    @patch('pandas.DataFrame.to_csv')
    def test_write_csv(self, mock_to_csv):
        dummy_df = pd.DataFrame([{
            '*题型': '单选',
            '*题目': 'Test',
            '*A': 'A',
            '*B': 'B',
            'C': '',
            'D': '',
            'E': '',
            'F': '',
            '*答案': 'A',
            '*难度': '简单',
            '试题解析': ''
        }])
        self.converter._write_csv(dummy_df)
        mock_to_csv.assert_called_once_with(
            self.target_file, 
            index=False, 
            encoding='utf-8-sig', 
            columns=['*题型', '*题目', '*A', '*B', 'C', 'D', 'E', 'F', '*答案', '*难度', '试题解析']
        )

    @patch.object(QuizFormatConverter, '_read_csv')
    @patch.object(QuizFormatConverter, '_write_csv')
    def test_convert_method(self, mock_write_csv, mock_read_csv):
        mock_read_csv.return_value = pd.DataFrame([
            {
                '试题题干(必填)': 'Question 1',
                '试题类型(必填)': '单选',
                '选项（用\'|\'隔开）': 'A. Opt1|B. Opt2',
                '答案（填空题用\'|\'隔开）': 'A',
                '难易度': '低',
                '试题解析': 'Parse 1'
            },
            {
                '试题题干(必填)': 'Question 2',
                '试题类型(必填)': '填空', # This should be skipped
                '选项（用\'|\'隔开）': '',
                '答案（填空题用\'|\'隔开）': '',
                '难易度': '中',
                '试题解析': 'Parse 2'
            }
        ])

        result = self.converter.convert()

        self.assertTrue(result['success'])
        self.assertEqual(result['converted_count'], 1)
        self.assertEqual(result['skipped_count'], 1)
        mock_write_csv.assert_called_once()


if __name__ == '__main__':
    unittest.main() 