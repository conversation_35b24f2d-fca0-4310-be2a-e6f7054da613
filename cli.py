import os
import sys
import logging
import argparse

# 添加项目根目录到Python路径，以便正确导入模块
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from config import Config
from quiz_processor.document_to_quiz_processor import DocumentToQuizProcessor
from csv_to_tdocx.csv_to_txt_converter import QuizToTxtConverter
from utils.helpers import (
    setup_logging, validate_input_directory, create_argument_parser,
    print_banner, print_progress_bar, validate_api_config,
    estimate_processing_time, summarize_results, scan_directory_structure
)
from error_handler.error_manager import ErrorManager

logger = logging.getLogger(__name__)

def create_argument_parser():
    parser = argparse.ArgumentParser(
        description="一个用于处理文档和生成测验的命令行工具。"
    )

    subparsers = parser.add_subparsers(dest="command", help="可用的命令")

    # process-docs 子命令
    process_parser = subparsers.add_parser('process-docs', help='处理文档并生成测验')
    process_parser.add_argument(
        "--input-dir",
        type=str,
        default=os.path.join(os.path.dirname(os.path.abspath(__file__)), "docs"),
        help="指定输入文档的目录。默认为当前脚本的 docs/ 子目录。"
    )
    process_parser.add_argument(
        "--no-recursive",
        action="store_true",
        help="禁用递归扫描子目录。"
    )
    process_parser.add_argument(
        "--cleanup-errors",
        type=int,
        metavar="DAYS",
        help="清理指定天数之前的错误日志文件。",
    )
    process_parser.add_argument(
        "--test-connection",
        action="store_true",
        help="仅测试API连接并退出。"
    )
    process_parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="启用详细日志输出（DEBUG级别）。"
    )

    # csv-to-txt 子命令
    csv_to_txt_parser = subparsers.add_parser('csv-to-txt', help='将CSV格式的题库转换为TXT格式')
    csv_to_txt_parser.add_argument(
        "--input-csv",
        type=str,
        required=True,
        help="指定CSV输入文件的路径。"
    )
    csv_to_txt_parser.add_argument(
        "--output-dir",
        type=str,
        default=".",
        help="指定TXT输出文件的目录。默认为当前目录。"
    )

    return parser

def main():
    print_banner()

    # 确保存在默认配置文件
    Config.create_default_config_if_missing()

    parser = create_argument_parser()
    args = parser.parse_args()

    # 设置日志级别
    log_level = "DEBUG" if args.verbose else "INFO"
    setup_logging(log_level)

    logger.info("正在启动命令行应用程序...")

    if args.command == 'csv-to-txt':
        logger.info(f"正在执行CSV到TXT转换命令: {args.input_csv} -> {args.output_dir}")
        try:
            converter = QuizToTxtConverter(config_path="新模板.ini")
            result = converter.convert(args.input_csv, args.output_dir)

            if result['status'] == "success":
                logger.info(f"转换成功！输出文件: {result['output_path']}")
                sys.exit(0)
            else:
                logger.error(f"转换失败: {result['message']}")
                sys.exit(1)
        except Exception as e:
            logger.exception(f"转换过程中发生意外错误: {e}")
            sys.exit(1)

    elif args.command == 'process-docs' or args.command is None: # 兼容旧的调用方式，如果未指定子命令则默认为process-docs
        # 加载配置
        config = Config.load_config()
        config = Config.update_from_args(config, args) # 从命令行参数更新配置

        # 验证输入目录
        if not validate_input_directory(args.input_dir):
            logger.error(f"输入目录无效或不存在: {args.input_dir}")
            sys.exit(1)

        # 如果指定了清理错误文件
        if args.cleanup_errors:
            error_manager = ErrorManager(config.ERROR_DIR)
            error_manager.cleanup_old_errors(args.cleanup_errors)
            logger.info(f"已清理 {args.cleanup_errors} 天前的错误文件。")
            sys.exit(0)

        # 仅测试API连接
        if args.test_connection:
            logger.info("正在测试API连接...")
            if validate_api_config(config):
                logger.info("✅ API连接成功！")
                sys.exit(0)
            else:
                logger.error("❌ API连接失败。请检查您的API密钥和API基础URL。")
                sys.exit(1)

        # 验证API配置
        if not validate_api_config(config):
            logger.error("API配置无效。请确保已设置API密钥或API基础URL。")
            sys.exit(1)

        try:
            processor = DocumentToQuizProcessor(config)

            # 扫描目录结构并处理文档
            all_files_to_process = scan_directory_structure(args.input_dir, recursive=not args.no_recursive)
            
            if not all_files_to_process:
                logger.info("没有找到需要处理的文档。")
                return

            logger.info(f"找到 {len(all_files_to_process)} 个文件需要处理。")
            
            # 估算处理时间
            estimated_time = estimate_processing_time(len(all_files_to_process))
            logger.info(f"预计处理时间: 约 {estimated_time:.2f} 分钟")

            processed_count = 0
            successful_count = 0
            failed_count = 0

            for i, doc_path in enumerate(all_files_to_process):
                logger.info(f"正在处理文件 [{i+1}/{len(all_files_to_process)}]: {doc_path}")
                try:
                    success = processor.process_document(doc_path)
                    if success:
                        successful_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    logger.error(f"处理文件 {doc_path} 时发生错误: {e}")
                    failed_count += 1
                finally:
                    processed_count += 1
                    print_progress_bar(processed_count, len(all_files_to_process))
            
            summarize_results(successful_count, failed_count)

        except Exception as e:
            logger.exception("命令行应用程序运行中发生未预期的错误:")
            sys.exit(1)

    else:
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main() 