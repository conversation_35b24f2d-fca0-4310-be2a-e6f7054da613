import pandas as pd

class CsvProcessor:
    def read_csv(self, file_path):
        try:
            df = pd.read_csv(file_path)
            print(f"DEBUG: 原始CSV列名: {df.columns.tolist()}") # Debug print to see raw column names
            # Clean up column names by stripping whitespace
            df.columns = df.columns.str.strip()
            print(f"DEBUG: 清理后CSV列名: {df.columns.tolist()}") # Debug print to see cleaned column names
            return df.to_dict(orient='records')
        except FileNotFoundError:
            raise FileNotFoundError(f"错误：未找到文件 {file_path}")
        except Exception as e:
            raise Exception(f"读取CSV文件时发生错误：{e}") 