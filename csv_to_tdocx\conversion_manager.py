from .csv_processor import CsvProcessor
from .template_parser import Template<PERSON>arser
from .docx_generator import DocxGenerator

class ConversionManager:
    def __init__(self):
        self.csv_processor = CsvProcessor()
        self.template_parser = TemplateParser()
        self.docx_generator = DocxGenerator()

    def convert_csv_to_tdocx(self, csv_file_path, template_file_path, output_file_path):
        try:
            # 1. 读取CSV文件
            csv_data = self.csv_processor.read_csv(csv_file_path)

            # 2. 加载模板文件
            templates = self.template_parser.load_template(template_file_path)

            # 3. 创建Word文档
            document = self.docx_generator.create_document()

            # 4. 遍历CSV数据并填充模板
            for i, row in enumerate(csv_data):
                csv_question_type = str(row.get('*题型', '')).strip() # Get type from CSV and strip whitespace, ensure it's a string
                print(f"DEBUG: 原始CSV题型: '{csv_question_type}'") # Debug print
                
                # Normalize question type from CSV to match template keys
                # This mapping should cover all discrepancies between CSV and INI
                question_type_mapping = {
                    '单选': '单选题',
                    '多选': '多选题',
                    '判断': '判断题',
                    '填空': '填空题',
                    '问答': '简答题', # Map '问答' from CSV to '简答题' in template
                    '简答': '简答题',
                    '排序': '排序题', # Assuming '排序题' template might exist or will be handled differently
                }
                
                # Use the mapped type, or fallback to the original if no specific mapping exists
                normalized_question_type = question_type_mapping.get(csv_question_type, csv_question_type)
                print(f"DEBUG: 映射后题型: '{normalized_question_type}'") # Debug print
                
                template = templates.get(normalized_question_type)

                if not template:
                    print(f"警告：未找到题型 '{csv_question_type}' (已尝试匹配 '{normalized_question_type}') 的模板，跳过此题。")
                    continue

                # 填充模板逻辑 (稍后实现)
                filled_content = self._fill_template(template, row, i + 1) # 题目编号从1开始
                self.docx_generator.add_text_to_document(document, filled_content)
                # 添加一个段落分隔符，使得每道题之间有空行，便于阅读
                self.docx_generator.add_text_to_document(document, "\n") 

            # 5. 保存Word文档
            self.docx_generator.save_document(document, output_file_path)
            print(f"成功生成tdocx文件：{output_file_path}")

        except FileNotFoundError as e:
            raise e
        except Exception as e:
            raise Exception(f"转换过程中发生错误：{e}")

    def _fill_template(self, template, data, question_number):
        # 这是一个占位符方法，将在后续子任务中详细实现
        # 示例：替换题目编号和题目内容
        filled_text = template.replace('{题目编号}', str(question_number))
        filled_text = filled_text.replace('{题目内容}', data.get('题目内容', ''))

        question_type = data.get('试题类型', '')
        answer = data.get('答案', '')
        options = data.get('选项', '')

        # 处理填空题答案格式转换（| -> 【/】）
        if question_type == '填空题' and answer:
            answer = answer.replace('|', '【/】')

        # 处理简答题答案格式转换（关键词 -> 关键字X：）
        if question_type == '简答题' and answer:
            keywords = [kw.strip() for kw in answer.split('|') if kw.strip()]
            formatted_keywords = [f'关键字{i+1}：{keyword}' for i, keyword in enumerate(keywords)]
            answer = '\n'.join(formatted_keywords)

        # 处理选项格式转换（| -> A、B、C、D、）
        formatted_options = []
        if options:
            option_list = [opt.strip() for opt in options.split('|') if opt.strip()]
            for i, opt in enumerate(option_list):
                formatted_options.append(f'{chr(65 + i)}、{opt}')
            options = '\n'.join(formatted_options)

        filled_text = filled_text.replace('{答案}', answer)
        filled_text = filled_text.replace('{选项}', options)
        filled_text = filled_text.replace('{解析}', data.get('解析', ''))
        return filled_text 