import re # Import re module for regular expressions

class TemplateParser:
    def __init__(self):
        self.templates = {}

    def load_template(self, file_path):
        self.templates = {}
        current_template_type = None
        current_template_content_lines = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Regex to find template headers like "1.【单选题】"
            template_header_pattern = re.compile(r'^\d+\.【(.*?)】')

            for line in lines:
                # Remove trailing newline for consistent processing
                stripped_line = line.strip()

                match = template_header_pattern.match(stripped_line)
                if match:
                    # Found a new template header
                    if current_template_type and current_template_content_lines:
                        # Save the previous template
                        self.templates[current_template_type] = "\n".join(current_template_content_lines).strip()
                    
                    current_template_type = match.group(1).strip() # Extract the type, e.g., "单选题"
                    current_template_content_lines = [stripped_line] # Start new content with the header line itself
                elif current_template_type is not None: # Only append if we are already within a template section
                    current_template_content_lines.append(stripped_line)
            
            # Save the last template after the loop finishes
            if current_template_type and current_template_content_lines:
                self.templates[current_template_type] = "\n".join(current_template_content_lines).strip()

            if not self.templates:
                raise Exception("未能在模板文件中找到任何题型模板。请检查模板格式是否正确。")

            return self.templates
        except FileNotFoundError:
            raise FileNotFoundError(f"错误：未找到模板文件 {file_path}")
        except Exception as e:
            raise Exception(f"解析模板文件时发生错误：{e}")

    pass 